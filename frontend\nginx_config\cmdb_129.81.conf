# 定义缓存路径和参数
proxy_cache_path /var/cache/nginx/cmdb levels=1:2 keys_zone=cmdb_cache:10m max_size=1g inactive=60m;
proxy_temp_path /var/cache/nginx/temp;

# 定义浏览器缓存映射
map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;  # 不缓存HTML
    text/css                   max;    # CSS文件长期缓存
    application/javascript     max;    # JS文件长期缓存
    ~image/                    max;    # 图片长期缓存
    ~font/                     max;    # 字体长期缓存
}

# 主服务器配置 - 80端口
server {
    listen       80;
    server_name  *************;

    # 启用浏览器缓存
    expires $expires;

    # 前端静态文件
    location / {
        root /home/<USER>/frontend/dist;
        try_files $uri $uri/ /index.html;
        index index.html;

        # 添加安全相关的HTTP头
        add_header X-Content-Type-Options nosniff;
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-XSS-Protection "1; mode=block";

        # 启用访问控制
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
    }

    # API代理 - 直接代理到后端服务
    location /api/ {
        # 添加调试信息
        add_header X-API-Proxy "direct-backend" always;

        proxy_pass http://127.0.0.1:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 确保请求方法不被改变
        proxy_method $request_method;
        proxy_pass_request_body on;
        proxy_pass_request_headers on;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 不缓存API响应
        proxy_cache_bypass $http_pragma $http_authorization;
        proxy_no_cache $http_pragma $http_authorization;
        
        # 处理OPTIONS请求，解决CORS预检问题
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "OK";
    }
}

# 后端服务器组 - 负载均衡配置
upstream cmdb-web {
    # 使用IP哈希算法，确保同一客户端总是访问同一服务器
    ip_hash;

    # 服务器列表 - 确保主服务器在前
    server 127.0.0.1:9000 weight=5 max_fails=3 fail_timeout=30s;
    # server *************:9000 weight=1 max_fails=3 fail_timeout=30s backup;

    # 启用keepalive连接
    keepalive 32;
}
