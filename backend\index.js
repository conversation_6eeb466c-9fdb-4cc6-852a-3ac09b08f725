// 引入 dotenv 并加载 .env 文件
require('dotenv').config();
// 引入框架 express
const express = require('express')
// 引入body-parse
const bodyParser = require('body-parser')
// 引入cors
const cors = require('cors')
// 引入报表API路由
const reportRoutes = require('./report_api')
// 引入自动发现模块
const discoveryRoutes = require('./services/discovery/index')
// 引入用户管理API
const userApi = require('./services/auth/user_api')
// 引入登录API
const loginApi = require('./services/auth/login_api')
// 引入页面权限API
const pagePermissionApi = require('./services/auth/page_permission_api_fixed')
// 引入权限验证中间件
const { pagePermissionMiddleware } = require('./services/auth/auth_middleware')
// 引入AI平台API
const aiPlatformApi = require('./services/ai/ai_platform_api')
// 引入变更管理控制器
const opsChangeManagementController = require('./controllers/ops_change_management')
// 引入变更模板管理控制器
const opsChangeTemplatesController = require('./controllers/ops_change_templates')
// 引入交易日历控制器
const opsCalendarController = require('./controllers/ops_calendar')
// 引入静态文件服务中间件
const createStaticFileMiddleware = require('./middlewares/static-files')
// 引入文件上传中间件
const { upload, uploadWithErrorHandling } = require('./utils/file-upload')

const app = express()
// app.use(cors())
// 配置 CORS，允许跨域请求携带凭证（Cookie）  后端监控前端应用的访问
app.use(cors({
    origin: (origin, callback) => {
        // 处理 origin 为 undefined 的情况
        if (!origin) {
            callback(null, '*');
            return;
        }
        const cleanOrigin = origin.replace(/\/$/, '');
        callback(null, cleanOrigin);
    },
    credentials: true,
}));

// 增加请求体大小限制
app.use(bodyParser.json({ limit: '20mb' }))
app.use(bodyParser.urlencoded({ extended: true, limit: '20mb' }))

//引入加密函数
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
// JWT 配置
const JWT_SECRET = process.env.JWT_SECRET || 'default-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '6h';

const { connPG } = require('./db/pg');
const { version } = require('./package.json');

// 添加版本号路由
app.get('/api/version', (req, res) => {
  try {
    console.log('收到版本请求:', req.url);

    // 添加CORS头，确保跨域访问正常
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET');
    res.header('Access-Control-Allow-Headers', 'Content-Type');

    // 获取版本信息
    const versionInfo = {
      version: version || '2.0.0.0', // 提供默认值避免空值
      success: true,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    };

    console.log('返回版本信息:', versionInfo);

    // 返回版本信息
    res.json(versionInfo);
  } catch (error) {
    console.error('获取版本信息错误:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      version: 'error'
    });
  }
});

// 添加一个简单的测试路由
app.get('/api/test', (req, res) => {
  res.json({ message: 'API服务器运行正常' });
});

// 数据库连接已在 ./db/pg.js 中配置

// 请求拦截中间件
function logRequests(req, res, next) {
    // 跳过登录路由
    if (req.path === '/api/get_cmdb_users_login') {
        return next();
    }

    const { method, originalUrl, body } = req;
    let username = 'anonymous';
    let operationType = null;
    let sanitizedBody = { ...body };

    // 敏感数据处理
    if (req.path.includes('password')) {
        // 密码相关操作只记录操作类型，不记录具体密码值
        if (sanitizedBody.password) sanitizedBody.password = '[REDACTED]';
        if (sanitizedBody.newPassword) sanitizedBody.newPassword = '[REDACTED]';
        if (sanitizedBody.currentPassword) sanitizedBody.currentPassword = '[REDACTED]';
    }

    // 根据请求路径设置 operationType
    if (req.path.startsWith('/api/add')) {
        operationType = 'add';
    } else if (req.path.startsWith('/api/del')) {
        operationType = 'delete';
    } else if (req.path.startsWith('/api/update')) {
        operationType = 'update';
    }

    if (!operationType) {
        return next();
    }

    // 从请求头中获取 JWT
    const token = req.headers.authorization?.split(' ')[1];

    if (token) {
        try {
            const decoded = jwt.verify(token, JWT_SECRET);
            username = decoded.username;
        } catch (err) {
            console.error('JWT 验证失败:', err);
            return res.json({ code: 1, msg: 'token验证失败' });
        }
    } else {
        return res.json({ code: 1, msg: '未找到有效的token' });
    }

    // 记录日志到数据库，使用脱敏后的body
    connPG.query(
        'INSERT INTO cmdb_user_logs(method, url, body, username, operation_type) VALUES($1, $2, $3, $4, $5) ON CONFLICT DO NOTHING',
        [method, originalUrl, sanitizedBody, username, operationType],
        (err, result) => {
            if (err) {
                console.error('Error logging request:', err.stack);
            }
        }
    );

    next();
}


// 应用请求拦截中间件
app.use(logRequests);

// 将数据库连接添加到应用程序中
app.set('connPG', connPG);

// 注册静态文件服务中间件
createStaticFileMiddleware(app);

// 使用报表API路由
app.use('/api', reportRoutes);

// 使用自动发现模块路由
app.use('/api/discovery', discoveryRoutes);

// 使用用户管理API
app.use('/api', userApi);

// 使用登录API
app.use('/api', loginApi);

// 使用页面权限API
app.use('/api', pagePermissionApi);

// 使用AI平台API
app.use('/api', aiPlatformApi);

// 变更管理API路由
app.post('/api/get_ops_change_management', opsChangeManagementController.getChangeManagementList);
app.post('/api/get_system_list', opsChangeManagementController.getSystemList);
app.post('/api/get_user_list', opsChangeManagementController.getUserList);
app.post('/api/add_ops_change_management', opsChangeManagementController.addChangeManagement);
app.post('/api/update_ops_change_management', opsChangeManagementController.updateChangeManagement);
app.post('/api/upload_ops_change_file', opsChangeManagementController.uploadFile);
app.post('/api/upload_ops_change_file_simple', opsChangeManagementController.uploadFileSimple); // 新增简化版文件上传API
app.get('/api/download_ops_change_file', opsChangeManagementController.downloadFile);
app.post('/api/remove_ops_change_file', opsChangeManagementController.removeFile);

// 变更模板管理API路由
app.post('/api/get_ops_change_templates', opsChangeTemplatesController.getTemplateList);

// 使用更简单的方式处理文件上传
const simpleUpload = upload.single('file');
app.post('/api/upload_ops_change_template', (req, res, next) => {
  // 设置文件类型
  req.query.fileType = 'change_template';

  // 处理文件上传
  simpleUpload(req, res, (err) => {
    if (err) {
      console.error('文件上传错误:', err);
      return res.status(400).json({ code: 1, msg: `文件上传失败: ${err.message}` });
    }
    next();
  });
}, opsChangeTemplatesController.uploadTemplate);

app.post('/api/overwrite_ops_change_template', (req, res, next) => {
  // 设置文件类型
  req.query.fileType = 'change_template';

  // 处理文件上传
  simpleUpload(req, res, (err) => {
    if (err) {
      console.error('文件覆盖上传错误:', err);
      return res.status(400).json({ code: 1, msg: `文件覆盖上传失败: ${err.message}` });
    }
    next();
  });
}, opsChangeTemplatesController.overwriteTemplate);

app.get('/api/download_ops_change_template', opsChangeTemplatesController.downloadTemplate);
app.post('/api/delete_ops_change_template', opsChangeTemplatesController.deleteTemplate);

// 交易日历API路由
app.post('/api/get_ops_calendar', opsCalendarController.getCalendarData);
app.post('/api/get_ops_calendar_changes', opsCalendarController.getDateChanges);
app.post('/api/get_ops_calendar_users', opsCalendarController.getUserList);
app.post('/api/update_ops_calendar_duty', opsCalendarController.updateDutySchedule);
app.post('/api/update_ops_calendar', opsCalendarController.updateCalendarData);
app.post('/api/check_ops_calendar_duty_permission', opsCalendarController.checkDutyPermission);
app.post('/api/update_ops_calendar_duty_permission', opsCalendarController.updateDutyPermission);

// 引入用户列表API
const userListApi = require('./services/auth/user_list_api');

// 引入IP资产管理API
const addHostScanResultsApi = require('./api/add_cmdb_host_scan_results');
const deleteHostScanResultsApi = require('./api/delete_cmdb_host_scan_results');

// 使用用户列表API
app.use('/api', userListApi);

// 使用IP资产管理API
app.use('/api', addHostScanResultsApi);
app.use('/api', deleteHostScanResultsApi);

// 添加页面权限验证中间件
app.use('/api/page_permission', pagePermissionMiddleware);

// 引入调度任务调度器
const { startScheduler, getSchedulerStatus } = require('./services/discovery/schedule-controller');

// 启动服务器
const server = app.listen(process.env.PORT, async () => {
    console.log('服务器启动成功');

    try {
        // 启动调度任务调度器
        const result = await startScheduler(app);
        if (result) {
            console.log('调度任务调度器已启动');
        } else {
            const status = await getSchedulerStatus(app);
            if (status.status === 'paused') {
                console.log('调度任务调度器处于暂停状态，未自动启动');
            } else {
                console.log('调度任务调度器启动失败');
            }
        }
    } catch (error) {
        console.error('启动调度任务调度器时发生错误:', error);
    }
})

app.post('/api/add_cmdb_monitored_ip_list', (req, res) => {
    const { ip_address, description, status, last_checked, usernameby } = req.body;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        INSERT INTO cmdb_monitored_ip_list (ip_address, description, status, last_checked, created_by)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *;  -- 返回插入的数据
    `;

    // 参数数组
    const queryParams = [ip_address, description, status, last_checked, usernameby];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('插入数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 返回插入的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/del_cmdb_monitored_ip_list', (req, res) => {
    const id = req.body.id;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_monitored_ip_list
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/update_cmdb_monitored_ip_list', (req, res) => {
    const { ip_address, description, status, last_checked, id, usernameby } = req.body;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_monitored_ip_list
        SET ip_address = $1, description = $2, status = $3, last_checked = $4,
            updated_at = CURRENT_TIMESTAMP, updated_by = '${usernameby}'
        WHERE id = $5
        RETURNING *;  -- 返回更新后的数据
    `;

    // 参数数组
    const queryParams = [ip_address, description, status, last_checked, id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('更新数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/get_cmdb_monitored_ip_list', async (req, res) => {


    const ip_address = req.body.ip_address;
    const page = parseInt(req.body.currentPage);
    const perPage = parseInt(req.body.pageSize);
    const sortProp = req.body.sortProp;
    const sortOrder = req.body.sortOrder;

    let sqlStr = `SELECT t.id, t.ip_address, t.last_checked,
                t.status, t.description, t.created_at, t.created_by, t.updated_at, t.updated_by
                FROM v_cmdb_monitored_ip_list t WHERE ($1::text IS NULL or ip_address LIKE $1)
       ORDER BY ${sortProp} ${sortOrder.toUpperCase()},id desc LIMIT $2 OFFSET $3`;
    const queryParams = [ip_address ? `%${ip_address}%` : null, perPage, (page - 1) * perPage];

    try {
        const result = await connPG.query(sqlStr, queryParams)

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM v_cmdb_monitored_ip_list WHERE ($1::text IS NULL or ip_address LIKE $1)`,
            [ip_address ? `%${ip_address}%` : null]
        );
        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) })
    } catch (err) {
        console.error('查询管理IP失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
})

app.post('/api/add_cmdb_device_management', (req, res) => {
    const fields = [
        'management_ip',
        'out_of_band_management',
        'hostname',
        'function_purpose',
        'admin1',
        'admin2',
        'device_type',
        'production_attributes',
        'data_center',
        'operation_status',
        'asset_number',
        'purchase_date',
        'maintenance_years',
        'maintenance_end_date',
        'serial_number',
        'model',
        'version',
        'cpu_model',
        'is_innovative_tech',
        'is_monitored',
        'monitoring_ip',
        'architecture_mode', // 注意：该字段已在前端注释，保留数据库字段作为后期扩展
        'is_single_point',
        'managed_addresses',
        'remarks',
        'year_category',
        'in_monitoring_list',
        'pre_monitoring_verified',
        'inspection',
        'created_by'
    ];

    // 使用扩展运算符从 req.body 中提取对应字段的值
    const { usernameby, ...params } = req.body;
    // 动态生成占位符
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
    // 构建 SQL 语句
    const sqlStr = `
       INSERT INTO cmdb_device_management (${fields.join(', ')})
       VALUES (${placeholders})
       RETURNING *;
   `;
    // 参数数组，确保包含 usernameby 对应的 created_by 字段
    const queryParams = fields.map(field => field === 'created_by' ? usernameby : params[field]);

    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('插入数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/del_cmdb_device_management', (req, res) => {
    const id = req.body.id;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_device_management
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/update_cmdb_device_management', (req, res) => {
    const fields = [
        'management_ip',
        'out_of_band_management',
        'hostname',
        'function_purpose',
        'admin1',
        'admin2',
        'device_type',
        'production_attributes',
        'data_center',
        'operation_status',
        'asset_number',
        'purchase_date',
        'maintenance_years',
        'maintenance_end_date',
        'serial_number',
        'model',
        'version',
        'cpu_model',
        'is_innovative_tech',
        'is_monitored',
        'monitoring_ip',
        'architecture_mode', // 注意：该字段已在前端注释，保留数据库字段作为后期扩展
        'is_single_point',
        'managed_addresses',
        'remarks',
        'year_category',
        'in_monitoring_list',
        'pre_monitoring_verified',
        'inspection',
        'updated_at',  // 新增字段
        'updated_by',  // 新增字段
        'id'
    ];
    // 使用解构赋值从 req.body 中提取 id 和 usernameby
    const { id, usernameby, ...updates } = req.body;

    // 动态生成带有占位符的SQL语句
    const setClauseParts = [];
    const queryParams = [];

    fields.slice(0, -1).forEach((field, index) => {
        if (field === 'updated_at') {
            setClauseParts.push(`${field} = CURRENT_TIMESTAMP`);
        } else if (field === 'updated_by') {
            setClauseParts.push(`${field} = $${queryParams.length + 1}`);
            queryParams.push(usernameby);
        } else {
            if (updates[field] !== undefined) {
                setClauseParts.push(`${field} = $${queryParams.length + 1}`);
                queryParams.push(updates[field]);
            }
        }
    });

    const sqlStr = `
        UPDATE cmdb_device_management
        SET ${setClauseParts.join(',\n')}
        WHERE id = $${queryParams.length + 1}
        RETURNING *;
    `;

    // 将 id 添加到参数数组末尾
    queryParams.push(id);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('更新数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/get_cmdb_device_management', async (req, res) => {

    const management_ip = req.body.management_ip;
    const out_of_band_management = req.body.out_of_band_management;
    const hostname = req.body.hostname;
    const admin1 = req.body.admin1;
    const admin2 = req.body.admin2;
    const device_type = req.body.device_type;
    const production_attributes = req.body.production_attributes;
    const data_center = req.body.data_center;
    const operation_status = req.body.operation_status;
    const is_monitored = req.body.is_monitored;
    const online_status = req.body.online_status;
    const is_innovative_tech = req.body.is_innovative_tech;
    const year_category = req.body.year_category;
    const serial_number = req.body.serial_number; // 添加序列号搜索条件

    const page = parseInt(req.body.currentPage);
    const perPage = parseInt(req.body.pageSize);
    const sortProp = req.body.sortProp;
    const sortOrder = req.body.sortOrder;

    let sqlStr = `SELECT T.ID,
	management_ip,
	out_of_band_management,
	hostname,
	function_purpose,
	admin1,
	admin2,
	device_type,
	production_attributes,
	data_center,
	operation_status,
	asset_number,
	purchase_date,
	maintenance_years,
	maintenance_end_date,
	serial_number,
	model,
	VERSION,
	is_innovative_tech,
	is_monitored,
    online_status,
    monitoring_ip,
	architecture_mode,
	is_single_point,
	managed_addresses,
	remarks,
	year_category,
	in_monitoring_list,
	pre_monitoring_verified,
	inspection, t.created_at, t.created_by, t.updated_at, t.updated_by
FROM
	v_cmdb_device_management t
 WHERE ($1::text IS NULL or management_ip LIKE $1) and
        ($4::text IS NULL or out_of_band_management LIKE $4) and
        ($5::text IS NULL or hostname LIKE $5) and
        ($6::text IS NULL or admin1 LIKE $6) and
        ($7::text IS NULL or admin2 LIKE $7) and
        ($8::text IS NULL or device_type = $8) and
        ($9::text IS NULL or production_attributes = $9) and
        ($10::text IS NULL or data_center = $10) and
        ($11::text IS NULL or operation_status = $11) and
        ($12::text IS NULL or is_monitored = $12) and
        ($13::text IS NULL or online_status = $13) and
        ($14::text IS NULL or is_innovative_tech = $14) and
        ($15::text IS NULL or year_category LIKE $15) and
        ($16::text IS NULL or serial_number LIKE $16)
       ORDER BY ${sortProp} ${sortOrder.toUpperCase()},id desc LIMIT $2 OFFSET $3`;
    const queryParams = [management_ip ? `%${management_ip}%` : null, perPage, (page - 1) * perPage,
    out_of_band_management ? `%${out_of_band_management}%` : null,
    hostname ? `%${hostname}%` : null,
    admin1 ? `%${admin1}%` : null,
    admin2 ? `%${admin2}%` : null,
    device_type ? `${device_type}` : null,
    production_attributes ? `${production_attributes}` : null,
    data_center ? `${data_center}` : null,
    operation_status ? `${operation_status}` : null,
    is_monitored ? `${is_monitored}` : null,
    online_status ? `${online_status}` : null,
    is_innovative_tech ? `${is_innovative_tech}` : null,
    year_category ? `%${year_category}%` : null,
    serial_number ? `%${serial_number}%` : null
    ];

    try {
        const result = await connPG.query(sqlStr, queryParams)

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM v_cmdb_device_management
 WHERE ($1::text IS NULL or management_ip LIKE $1) and
        (1=1 or $2::text is null or $3::text is null) and
($4::text IS NULL or out_of_band_management LIKE $4) and
($5::text IS NULL or hostname LIKE $5) and
($6::text IS NULL or admin1 LIKE $6) and
($7::text IS NULL or admin2 LIKE $7) and
($8::text IS NULL or device_type = $8) and
($9::text IS NULL or production_attributes = $9) and
($10::text IS NULL or data_center = $10) and
($11::text IS NULL or operation_status = $11) and
($12::text IS NULL or is_monitored = $12) and
($13::text IS NULL or online_status = $13) and
($14::text IS NULL or is_innovative_tech = $14) and
($15::text IS NULL or year_category LIKE $15) and
($16::text IS NULL or serial_number LIKE $16)`,
        queryParams
        );
        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) })
    } catch (err) {
        console.error('查询管理IP失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});

app.post('/api/add_cmdb_server_management', (req, res) => {
    const fields = [
        'management_ip',
        'hostname',
        'function_purpose',
        'admin1',
        'admin2',
        'server_type',
        'production_attributes',
        'data_center',
        'out_of_band_management_ilo',
        'operation_status',
        'is_innovative_tech',
        'asset_number',
        'purchase_date',
        'maintenance_years',
        'maintenance_end_date',
        'serial_number',
        'server_model',
        'is_monitored',
        'cpu_model',
        'memory',
        'disk',
        'network_card',
        'operating_system',
        'os_category',
        'remarks',
        'year_category',
        'weak_password_exists',
        'weak_password_correction_date',
        'is_single_point',
        'managed_addresses',
        'created_by'
    ];

    // 使用扩展运算符从 req.body 中提取对应字段的值
    const { usernameby, ...params } = req.body;
    // 动态生成占位符
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
    // 构建 SQL 语句
    const sqlStr = `
       INSERT INTO cmdb_server_management (${fields.join(', ')})
       VALUES (${placeholders})
       RETURNING *;
   `;
    // 参数数组，确保包含 usernameby 对应的 created_by 字段
    const queryParams = fields.map(field => field === 'created_by' ? usernameby : params[field]);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('插入数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 返回插入的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/del_cmdb_server_management', (req, res) => {
    const id = req.body.id;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_server_management
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/update_cmdb_server_management', (req, res) => {
    const fields = [
        'management_ip',
        'hostname',
        'function_purpose',
        'admin1',
        'admin2',
        'server_type',
        'production_attributes',
        'data_center',
        'out_of_band_management_ilo',
        'operation_status',
        'is_innovative_tech',
        'asset_number',
        'purchase_date',
        'maintenance_years',
        'maintenance_end_date',
        'serial_number',
        'server_model',
        'is_monitored',
        'cpu_model',
        'memory',
        'disk',
        'network_card',
        'operating_system',
        'os_category',
        'remarks',
        'year_category',
        'weak_password_exists',
        'weak_password_correction_date',
        'is_single_point',
        'managed_addresses',
        'updated_at',  // 新增字段
        'updated_by',  // 新增字段
        'id'
    ];

    // 使用解构赋值从 req.body 中提取 id 和 usernameby
    const { id, usernameby, ...updates } = req.body;

    // 动态生成带有占位符的SQL语句
    const setClauseParts = [];
    const queryParams = [];

    fields.slice(0, -1).forEach((field, index) => {
        if (field === 'updated_at') {
            setClauseParts.push(`${field} = CURRENT_TIMESTAMP`);
        } else if (field === 'updated_by') {
            setClauseParts.push(`${field} = $${queryParams.length + 1}`);
            queryParams.push(usernameby);
        } else {
            if (updates[field] !== undefined) {
                setClauseParts.push(`${field} = $${queryParams.length + 1}`);
                queryParams.push(updates[field]);
            }
        }
    });

    const sqlStr = `
        UPDATE cmdb_server_management
        SET ${setClauseParts.join(',\n')}
        WHERE id = $${queryParams.length + 1}
        RETURNING *;
    `;

    // 将 id 添加到参数数组末尾
    queryParams.push(id);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('更新数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/get_cmdb_server_management', async (req, res) => {

    const management_ip = req.body.management_ip;
    const os_category = req.body.os_category;
    const hostname = req.body.hostname;
    const server_type = req.body.server_type;
    const admin1 = req.body.admin1;
    const admin2 = req.body.admin2;
    const production_attributes = req.body.production_attributes;
    const data_center = req.body.data_center;
    const operation_status = req.body.operation_status;
    const is_monitored = req.body.is_monitored;
    const online_status = req.body.online_status;
    const is_innovative_tech = req.body.is_innovative_tech;
    const year_category = req.body.year_category;
    const out_of_band_management_ilo = req.body.out_of_band_management_ilo;

    const page = parseInt(req.body.currentPage);
    const perPage = parseInt(req.body.pageSize);
    const sortProp = req.body.sortProp;
    const sortOrder = req.body.sortOrder;

    let sqlStr = `SELECT T
	.ID,
	management_ip,
	hostname,
	function_purpose,
	admin1,
	admin2,
	server_type,
	production_attributes,
	data_center,
	out_of_band_management_ilo,
	operation_status,
	is_innovative_tech,
	is_single_point,
	managed_addresses,
	asset_number,
	purchase_date,
	maintenance_years,
	maintenance_end_date,
	serial_number,
	server_model,
    is_monitored,
    online_status,
	cpu_model,
	memory,
	disk,
	network_card,
	operating_system,
    os_category,
	remarks,
	year_category,
	weak_password_exists,
	weak_password_correction_date, t.created_at, t.created_by, t.updated_at, t.updated_by
FROM v_cmdb_server_management t
WHERE ($1::text IS NULL or management_ip LIKE $1) and
($4::text IS NULL or os_category LIKE $4) and
($5::text IS NULL or hostname LIKE $5) and
($6::text IS NULL or server_type = $6) and
($7::text IS NULL or admin1 LIKE $7) and
($8::text IS NULL or admin2 LIKE $8) and
($9::text IS NULL or production_attributes = $9) and
($10::text IS NULL or data_center = $10) and
($11::text IS NULL or operation_status = $11) and
($12::text IS NULL or is_monitored = $12) and
($13::text IS NULL or online_status = $13) and
($14::text IS NULL or is_innovative_tech = $14) and
($15::text IS NULL or year_category LIKE $15) and
($16::text IS NULL or out_of_band_management_ilo LIKE $16)
       ORDER BY ${sortProp} ${sortOrder.toUpperCase()},id desc LIMIT $2 OFFSET $3`;
    const queryParams = [management_ip ? `%${management_ip}%` : null, perPage, (page - 1) * perPage,
        os_category ? `%${os_category}%` : null,
        hostname ? `%${hostname}%` : null,
        server_type ? `${server_type}` : null,
        admin1 ? `%${admin1}%` : null,
        admin2 ? `%${admin2}%` : null,
        production_attributes ? `${production_attributes}` : null,
        data_center ? `${data_center}` : null,
        operation_status ? `${operation_status}` : null,
        is_monitored ? `${is_monitored}` : null,
        online_status ? `${online_status}` : null,
        is_innovative_tech ? `${is_innovative_tech}` : null,
        year_category ? `%${year_category}%` : null,
        out_of_band_management_ilo ? `%${out_of_band_management_ilo}%` : null
    ];

    try {
        const result = await connPG.query(sqlStr, queryParams)

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM v_cmdb_server_management
WHERE ($1::text IS NULL or management_ip LIKE $1) and
        (1=1 or $2::text is null or $3::text is null) and
($4::text IS NULL or os_category LIKE $4) and
($5::text IS NULL or hostname LIKE $5) and
($6::text IS NULL or server_type = $6) and
($7::text IS NULL or admin1 LIKE $7) and
($8::text IS NULL or admin2 LIKE $8) and
($9::text IS NULL or production_attributes = $9) and
($10::text IS NULL or data_center = $10) and
($11::text IS NULL or operation_status = $11) and
($12::text IS NULL or is_monitored = $12) and
($13::text IS NULL or online_status = $13) and
($14::text IS NULL or is_innovative_tech = $14) and
($15::text IS NULL or year_category LIKE $15) and
($16::text IS NULL or out_of_band_management_ilo LIKE $16)`,
            queryParams
            //'SELECT COUNT(*) as total FROM cmdb_device_management'
        );
        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) })
    } catch (err) {
        console.error('查询管理IP失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
})


app.post('/api/add_cmdb_vm_registry', (req, res) => {
    const fields = [
        'management_ip',
        'hostname',
        'function_purpose',
        'admin1',
        'admin2',
        'host_ip',
        'operating_system',
        'data_center1',
        'admin',
        'app_system_id',
        'virtual_host_ip',
        'data_center2',
        'is_monitored',
        'weak_password_exists',
        'weak_password_correction_date',
        'created_by'
    ];

    // 使用扩展运算符从 req.body 中提取对应字段的值
    const { usernameby, ...params } = req.body;
    // 动态生成占位符
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
    // 构建 SQL 语句
    const sqlStr = `
       INSERT INTO cmdb_vm_registry (${fields.join(', ')})
       VALUES (${placeholders})
       RETURNING *;
   `;
    // 参数数组，确保包含 usernameby 对应的 created_by 字段
    const queryParams = fields.map(field => field === 'created_by' ? usernameby : params[field]);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('插入数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 返回插入的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/del_cmdb_vm_registry', (req, res) => {
    const id = req.body.id;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_vm_registry
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/update_cmdb_vm_registry', (req, res) => {
    const fields = [
        'management_ip',
        'hostname',
        'function_purpose',
        'admin1',
        'admin2',
        'host_ip',
        'operating_system',
        'data_center1',
        'admin',
        'app_system_id',
        'virtual_host_ip',
        'data_center2',
        'is_monitored',
        'weak_password_exists',
        'weak_password_correction_date',
        'updated_at',  // 新增字段
        'updated_by',  // 新增字段
        'id'
    ];
    const { id, usernameby, ...updates } = req.body;

    // 动态生成带有占位符的SQL语句
    const setClauseParts = [];
    const queryParams = [];

    fields.slice(0, -1).forEach((field, index) => {
        if (field === 'updated_at') {
            setClauseParts.push(`${field} = CURRENT_TIMESTAMP`);
        } else if (field === 'updated_by') {
            setClauseParts.push(`${field} = $${queryParams.length + 1}`);
            queryParams.push(usernameby);
        } else {
            if (updates[field] !== undefined) {
                setClauseParts.push(`${field} = $${queryParams.length + 1}`);
                queryParams.push(updates[field]);
            }
        }
    });

    const sqlStr = `
        UPDATE cmdb_vm_registry
        SET ${setClauseParts.join(',\n')}
        WHERE id = $${queryParams.length + 1}
        RETURNING *;
    `;

    // 将 id 添加到参数数组末尾
    queryParams.push(id);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('更新数据失败:', err);
            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/get_cmdb_vm_registry', async (req, res) => {

    const management_ip = req.body.management_ip;
    const hostname = req.body.hostname;
    const admin1 = req.body.admin1;
    const admin2 = req.body.admin2;
    const host_ip = req.body.host_ip;
    const data_center1 = req.body.data_center1;
    const app_system_id = req.body.app_system_id;
    const is_monitored = req.body.is_monitored;
    const online_status = req.body.online_status;


    const page = parseInt(req.body.currentPage);
    const perPage = parseInt(req.body.pageSize);
    const sortProp = req.body.sortProp;
    const sortOrder = req.body.sortOrder;

    let sqlStr = `SELECT T.ID,
	T.management_ip,
	T.hostname,
	T.function_purpose,
	T.admin1,
	T.admin2,
	T.host_ip,
	operating_system,
	data_center1,
	app_system_id,
    is_monitored,
    online_status,
	T.weak_password_exists,
	weak_password_correction_date, t.created_at, t.created_by, t.updated_at, t.updated_by
FROM v_cmdb_vm_registry T
 WHERE ($1::text IS NULL or t.management_ip LIKE $1) and
 ($4::text IS NULL or t.hostname LIKE $4) and
 ($5::text IS NULL or t.admin1 LIKE $5) and
 ($6::text IS NULL or t.admin2 LIKE $6) and
 ($7::text IS NULL or t.host_ip LIKE $7) and
 ($8::text IS NULL or t.data_center1 = $8) and
 ($9::text IS NULL or t.app_system_id LIKE $9) and
 ($10::text IS NULL or t.is_monitored = $10) and
 ($11::text IS NULL or t.online_status = $11)
       ORDER BY ${sortProp} ${sortOrder.toUpperCase()},id desc LIMIT $2 OFFSET $3`;

    const queryParams = [management_ip ? `%${management_ip}%` : null, perPage, (page - 1) * perPage,
        hostname ? `%${hostname}%` : null,
        admin1 ? `%${admin1}%` : null,
        admin2 ? `%${admin2}%` : null,
        host_ip ? `%${host_ip}%` : null,
        data_center1 ? `${data_center1}` : null,
        app_system_id ? `%${app_system_id}%` : null,
        is_monitored ? `${is_monitored}` : null,
        online_status ? `${online_status}` : null
    ];

    try {
        const result = await connPG.query(sqlStr, queryParams)

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM v_cmdb_vm_registry t
 WHERE ($1::text IS NULL or t.management_ip LIKE $1) and
        (1=1 or $2::text is null or $3::text is null) and
 ($4::text IS NULL or t.hostname LIKE $4) and
 ($5::text IS NULL or t.admin1 LIKE $5) and
 ($6::text IS NULL or t.admin2 LIKE $6) and
 ($7::text IS NULL or t.host_ip LIKE $7) and
 ($8::text IS NULL or t.data_center1 = $8) and
 ($9::text IS NULL or t.app_system_id LIKE $9) and
 ($10::text IS NULL or t.is_monitored = $10) and
 ($11::text IS NULL or t.online_status = $11)`,
 queryParams
        );
        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) })
    } catch (err) {
        console.error('查询管理IP失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
})


app.post('/api/add_cmdb_application_system_info', (req, res) => {
    const fields = [
        'management_ip',
        'hostname',
        'function_purpose',
        'server_admin1',
        'server_admin2',
        'data_center',
        'machine_usage_status',
        'remarks',
        'business_system_name',
        'system_administrator',
        'system_classification',
        'is_monitored',
        'deployed_applications',
        'production_attributes',
        'master_slave_role',
        'related_master_slave_ips',
        'backup_mode',
        'internet_ip',
        'internet_port',
        'operating_system',
        'has_antivirus_software',
        'patch_update_configured',
        'has_system_administrator',
        'created_by'
    ];

    // 使用扩展运算符从 req.body 中提取对应字段的值，确保不包含id字段
    const { usernameby, id, ...params } = req.body;
    // 动态生成占位符
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
    // 构建 SQL 语句
    const sqlStr = `
       INSERT INTO cmdb_application_system_info (${fields.join(', ')})
       VALUES (${placeholders})
       RETURNING *;
   `;
    // 参数数组，确保包含 usernameby 对应的 created_by 字段
    const queryParams = fields.map(field => field === 'created_by' ? usernameby : params[field]);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('插入数据失败:', err);

            // 如果是主键冲突错误，尝试重置序列
            if (err.code === '23505' && err.constraint === 'application_system_info_pkey') {
                // 查询当前最大ID
                connPG.query('SELECT MAX(id) as max_id FROM cmdb_application_system_info', (maxIdErr, maxIdResult) => {
                    if (maxIdErr) {
                        return res.status(500).json({ code: 1, msg: '查询最大ID失败: ' + maxIdErr.message });
                    }

                    const maxId = maxIdResult.rows[0].max_id || 0;
                    // 重置序列到最大ID+1
                    connPG.query(`ALTER SEQUENCE application_system_info_id_seq RESTART WITH ${maxId + 1}`, (resetErr) => {
                        if (resetErr) {
                            return res.status(500).json({ code: 1, msg: '重置序列失败: ' + resetErr.message });
                        }

                        // 重新尝试插入
                        connPG.query(sqlStr, queryParams, (retryErr, retryResult) => {
                            if (retryErr) {
                                return res.status(500).json({ code: 1, msg: '重试插入失败: ' + retryErr.message });
                            }

                            res.json({ code: 0, msg: retryResult.rows[0] });
                        });
                    });
                });
                return;
            }

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 返回插入的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/del_cmdb_application_system_info', (req, res) => {
    const id = req.body.id;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_application_system_info
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/update_cmdb_application_system_info', (req, res) => {
    const fields = [
        'management_ip',
        'hostname',
        'function_purpose',
        'server_admin1',
        'server_admin2',
        'data_center',
        'machine_usage_status',
        'remarks',
        'business_system_name',
        'system_administrator',
        'system_classification',
        'is_monitored',
        'deployed_applications',
        'production_attributes',
        'master_slave_role',
        'related_master_slave_ips',
        'backup_mode',
        'internet_ip',
        'internet_port',
        'operating_system',
        'has_antivirus_software',
        'patch_update_configured',
        'has_system_administrator',
        'updated_at',  // 新增字段
        'updated_by',  // 新增字段
        'id'
    ];
    // 使用解构赋值从 req.body 中提取 id 和 usernameby
    const { id, usernameby, ...updates } = req.body;

    // 动态生成带有占位符的SQL语句
    const setClauseParts = [];
    const queryParams = [];

    fields.slice(0, -1).forEach((field, index) => {
        if (field === 'updated_at') {
            setClauseParts.push(`${field} = CURRENT_TIMESTAMP`);
        } else if (field === 'updated_by') {
            setClauseParts.push(`${field} = $${queryParams.length + 1}`);
            queryParams.push(usernameby);
        } else {
            if (updates[field] !== undefined) {
                setClauseParts.push(`${field} = $${queryParams.length + 1}`);
                queryParams.push(updates[field]);
            }
        }
    });

    const sqlStr = `
        UPDATE cmdb_application_system_info
        SET ${setClauseParts.join(',\n')}
        WHERE id = $${queryParams.length + 1}
        RETURNING *;
    `;

    // 将 id 添加到参数数组末尾
    queryParams.push(id);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('更新数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/get_cmdb_application_system_info', async (req, res) => {
    console.log('收到查询请求，参数:', JSON.stringify(req.body));

    const management_ip = req.body.management_ip;
    const hostname = req.body.hostname;
    const function_purpose = req.body.function_purpose;
    const server_admin1 = req.body.server_admin1;
    const server_admin2 = req.body.server_admin2;
    const data_center = req.body.data_center;
    const machine_usage_status = req.body.machine_usage_status;
    const business_system_name = req.body.business_system_name;
    const system_classification = req.body.system_classification;
    const is_monitored = req.body.is_monitored;
    const production_attributes = req.body.production_attributes;

    const page = parseInt(req.body.currentPage);
    const perPage = parseInt(req.body.pageSize);
    const sortProp = req.body.sortProp;
    const sortOrder = req.body.sortOrder;

    let sqlStr = `SELECT t.id,
t.management_ip,
 hostname,
function_purpose,
server_admin1,
server_admin2,
data_center,  --- 需要取字典表
machine_usage_status,
remarks,
business_system_name,
system_administrator,
system_classification,  -- 需要先取其它表，再取字典
is_monitored,
deployed_applications,
production_attributes,
master_slave_role,
related_master_slave_ips,
backup_mode,
internet_ip,
internet_port,
operating_system, -- 需要先取其它表，再取字典
has_antivirus_software,
patch_update_configured,
has_system_administrator, t.created_at, t.created_by, t.updated_at, t.updated_by
 FROM v_cmdb_application_system_info T
WHERE ($1::text IS NULL or t.management_ip LIKE $1) and
($4::text IS NULL or t.hostname LIKE $4) and
($5::text IS NULL or t.function_purpose LIKE $5) and
($6::text IS NULL or t.server_admin1 LIKE $6) and
($7::text IS NULL or t.server_admin2 LIKE $7) and
($8::text IS NULL or t.data_center = $8) and
($9::text IS NULL or t.machine_usage_status LIKE $9) and
($10::text IS NULL or t.business_system_name LIKE $10) and
($11::text IS NULL or t.system_classification = $11) and
($12::text IS NULL or t.is_monitored = $12) and
($13::text IS NULL or t.production_attributes = $13)
       ORDER BY ${sortProp} ${sortOrder.toUpperCase()},id desc LIMIT $2 OFFSET $3`;
    const queryParams = [management_ip ? `%${management_ip}%` : null, perPage, (page - 1) * perPage,
        hostname ? `%${hostname}%` : null,
        function_purpose ? `%${function_purpose}%` : null,
        server_admin1 ? `%${server_admin1}%` : null,
        server_admin2 ? `%${server_admin2}%` : null,
        data_center ? `${data_center}` : null,
        machine_usage_status ? `%${machine_usage_status}%` : null,
        business_system_name ? `%${business_system_name}%` : null,
        system_classification ? `${system_classification}` : null,
        is_monitored ? `${is_monitored}` : null,
        production_attributes ? `${production_attributes}` : null,
    ];

    try {
        const result = await connPG.query(sqlStr, queryParams)

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM v_cmdb_application_system_info t
WHERE ($1::text IS NULL or t.management_ip LIKE $1) and
(1=1 or $2::text is null or $3::text is null) and
($4::text IS NULL or t.hostname LIKE $4) and
($5::text IS NULL or t.function_purpose LIKE $5) and
($6::text IS NULL or t.server_admin1 LIKE $6) and
($7::text IS NULL or t.server_admin2 LIKE $7) and
($8::text IS NULL or t.data_center = $8) and
($9::text IS NULL or t.machine_usage_status LIKE $9) and
($10::text IS NULL or t.business_system_name LIKE $10) and
($11::text IS NULL or t.system_classification = $11) and
($12::text IS NULL or t.is_monitored = $12) and
($13::text IS NULL or t.production_attributes = $13) `,
queryParams
        );
        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) })
    } catch (err) {
        console.error('查询管理IP失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});


app.post('/api/get_cmdb_host_scan_results', async (req, res) => {
    const management_ip = req.body.management_ip;
    const function_purpose = req.body.function_purpose;
    const admin1 = req.body.admin1;
    const admin2 = req.body.admin2;
    const designated_admin = req.body.designated_admin;
    const management_status = req.body.management_status;
    const is_virtual_machine = req.body.is_virtual_machine;
    const online_status = req.body.online_status; // 是否在线
    const cmdb_registration_status = req.body.cmdb_registration_status;

    const page = parseInt(req.body.currentPage);
    const perPage = parseInt(req.body.pageSize);
    const sortProp = req.body.sortProp;
    const sortOrder = req.body.sortOrder;

    let sqlStr = `SELECT T.id,
T.management_ip,
function_purpose,
admin1,
admin2,
designated_admin,
datacenter,
management_status,
is_virtual_machine,
online_status,
cmdb_registration_status,
remarks,
created_at,
created_by,
updated_at,
updated_by
  from v_cmdb_host_scan_results T
WHERE ($1::text IS NULL or t.management_ip LIKE $1)
  AND ($4::text IS NULL or t.function_purpose LIKE $4)
  AND ($5::text IS NULL or t.admin1 LIKE $5)
  AND ($6::text IS NULL or t.admin2 LIKE $6)
  AND ($7::text IS NULL or t.designated_admin LIKE $7)
  AND ($8::text IS NULL or t.management_status LIKE $8)
  AND ($9::text IS NULL or t.is_virtual_machine LIKE $9)
  AND ($10::text IS NULL or t.online_status LIKE $10)
  AND ($11::text IS NULL or t.cmdb_registration_status LIKE $11)
       ORDER BY ${sortProp} ${sortOrder.toUpperCase()},id desc LIMIT $2 OFFSET $3`;
    const queryParams = [
        management_ip ? `%${management_ip}%` : null,
        perPage,
        (page - 1) * perPage,
        function_purpose ? `%${function_purpose}%` : null,
        admin1 ? `%${admin1}%` : null,
        admin2 ? `%${admin2}%` : null,
        designated_admin ? `%${designated_admin}%` : null,
        management_status ? `%${management_status}%` : null,
        is_virtual_machine ? `%${is_virtual_machine}%` : null,
        online_status ? `%${online_status}%` : null,
        cmdb_registration_status ? `%${cmdb_registration_status}%` : null
    ];

    try {
        const result = await connPG.query(sqlStr, queryParams)

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM v_cmdb_host_scan_results
WHERE ($1::text IS NULL or management_ip LIKE $1) and
(1=1 or $2::text is null or $3::text is null)
  AND ($4::text IS NULL or function_purpose LIKE $4)
  AND ($5::text IS NULL or admin1 LIKE $5)
  AND ($6::text IS NULL or admin2 LIKE $6)
  AND ($7::text IS NULL or designated_admin LIKE $7)
  AND ($8::text IS NULL or management_status LIKE $8)
  AND ($9::text IS NULL or is_virtual_machine LIKE $9)
  AND ($10::text IS NULL or online_status LIKE $10)
  AND ($11::text IS NULL or cmdb_registration_status LIKE $11)`,
            queryParams
        );
        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) })
    } catch (err) {
        console.error('查询管理IP失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
})

app.post('/api/update_cmdb_host_scan_results', async (req, res) => {
    const { id, designated_admin, remarks, usernameby } = req.body;

    try {
        // 获取当前记录的IP地址
        const getIpQuery = `
            SELECT management_ip FROM cmdb_host_scan_results
            WHERE id = $1 AND del_flag = '0'
        `;
        const ipResult = await connPG.query(getIpQuery, [id]);

        if (ipResult.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        const currentIp = ipResult.rows[0].management_ip;

        // 检查IP是否为虚拟机
        const checkVmQuery = `
            SELECT 1 FROM cmdb_vm_registry
            WHERE management_ip = $1 AND del_flag = '0'
        `;
        const checkVmResult = await connPG.query(checkVmQuery, [currentIp]);
        const isVirtualMachine = checkVmResult.rows.length > 0 ? '是' : '否';

        // 构建带有占位符的SQL语句
        const sqlStr = `
            UPDATE cmdb_host_scan_results
            SET designated_admin = $1,
                remarks = $2,
                is_virtual_machine = $3,
                updated_at = CURRENT_TIMESTAMP,
                updated_by = $4
            WHERE id = $5
            RETURNING *;  -- 返回更新后的数据
        `;

        // 参数数组
        const queryParams = [designated_admin, remarks, isVirtualMachine, usernameby, id];

        // 执行查询
        const result = await connPG.query(sqlStr, queryParams);

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    } catch (err) {
        console.error('更新数据失败:', err);
        return res.status(500).json({ code: 1, msg: err.message });
    }
});


app.post('/api/add_cmdb_system_admin_responsibility_company', (req, res) => {
    const fields = [
        'self_build_system_id',
        'system_abbreviation',
        'main_admin',
        'backup_admin',
        'business_department',
        'system_attribute',
        'go_live_date',
        'decommission_date',
        'major_milestones',
        'industry_name',
        'monitoring_system_name',
        'system_function_summary',
        'system_form',
        'cs_client_name',
        'bs_url',
        'ip_port',
        'business_line',
        'system_category',
        'system_level',
        'has_backup_strategy',
        'server_count',
        'remarks',
        'monitoring_coverage',
        'digital_classification',
        'jrt_0059_backup_standard',
        'xinchuang_category_major',
        'xinchuang_category_minor',
        'software_copyright_name',
        'construction_method',
        'technical_route',
        'operation_status',
        'xinchuang_status',
        'security_level',
        'general_function_domains',
        'futures_function_domains',
        'is_reported_to_external',
        'centos7_count',
        'created_by'
    ];

    // 使用扩展运算符从 req.body 中提取对应字段的值
    const { usernameby, ...params } = req.body;
    // 动态生成占位符
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
    // 构建 SQL 语句
    const sqlStr = `
       INSERT INTO cmdb_system_admin_responsibility_company (${fields.join(', ')})
       VALUES (${placeholders})
       RETURNING *;
   `;
    // 参数数组，确保包含 usernameby 对应的 created_by 字段
    const queryParams = fields.map(field => field === 'created_by' ? usernameby : params[field]);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('插入数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 返回插入的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/del_cmdb_system_admin_responsibility_company', (req, res) => {
    const id = req.body.id;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_system_admin_responsibility_company
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/update_cmdb_system_admin_responsibility_company', (req, res) => {
    const fields = [
        'self_build_system_id',
        'system_abbreviation',
        'main_admin',
        'backup_admin',
        'business_department',
        'system_attribute',
        'go_live_date',
        'decommission_date',
        'major_milestones',
        'industry_name',
        'monitoring_system_name',
        'system_function_summary',
        'system_form',
        'cs_client_name',
        'bs_url',
        'ip_port',
        'business_line',
        'system_category',
        'system_level',
        'has_backup_strategy',
        'server_count',
        'remarks',
        'monitoring_coverage',
        'digital_classification',
        'jrt_0059_backup_standard',
        'xinchuang_category_major',
        'xinchuang_category_minor',
        'software_copyright_name',
        'construction_method',
        'technical_route',
        'operation_status',
        'xinchuang_status',
        'security_level',
        'general_function_domains',
        'futures_function_domains',
        'is_reported_to_external',
        'centos7_count',
        'updated_at',  // 新增字段
        'updated_by',  // 新增字段
        'id'
    ];
    // 使用解构赋值从 req.body 中提取 id 和 usernameby
    const { id, usernameby, ...updates } = req.body;

    // 动态生成带有占位符的SQL语句
    const setClauseParts = [];
    const queryParams = [];

    fields.slice(0, -1).forEach((field, index) => {
        if (field === 'updated_at') {
            setClauseParts.push(`${field} = CURRENT_TIMESTAMP`);
        } else if (field === 'updated_by') {
            setClauseParts.push(`${field} = $${queryParams.length + 1}`);
            queryParams.push(usernameby);
        } else {
            if (updates[field] !== undefined) {
                setClauseParts.push(`${field} = $${queryParams.length + 1}`);
                queryParams.push(updates[field]);
            }
        }
    });

    const sqlStr = `
        UPDATE cmdb_system_admin_responsibility_company
        SET ${setClauseParts.join(',\n')}
        WHERE id = $${queryParams.length + 1}
        RETURNING *;
    `;

    // 将 id 添加到参数数组末尾
    queryParams.push(id);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('更新数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/get_cmdb_system_admin_responsibility_company', async (req, res) => {
    try {
        const self_build_system_id = req.body.self_build_system_id;
        const system_abbreviation = req.body.system_abbreviation;
        const main_admin = req.body.main_admin;
        const system_attribute = req.body.system_attribute;
        const system_level = req.body.system_level;
        const jrt_0059_backup_standard = req.body.jrt_0059_backup_standard;
        const xinchuang_category_major = req.body.xinchuang_category_major;
        const xinchuang_category_minor = req.body.xinchuang_category_minor;
        const construction_method = req.body.construction_method;
        const technical_route = req.body.technical_route;
        const is_reported_to_external = req.body.is_reported_to_external;
        const operation_status = req.body.operation_status;
        const xinchuang_status = req.body.xinchuang_status;
        const security_level = req.body.security_level;
        const general_function_domain = req.body.general_function_domain;
        const futures_function_domain = req.body.futures_function_domain;
        const business_line = req.body.business_line;
        const system_category = req.body.system_category;

        const page = parseInt(req.body.currentPage) || 1;
        const perPage = parseInt(req.body.pageSize) || 10;
        const sortProp = req.body.sortProp || 'system_abbreviation';
        const sortOrder = req.body.sortOrder || 'asc';

        let sqlStr = `SELECT t.id,
            self_build_system_id,
            system_abbreviation,
            main_admin,
            backup_admin,
            business_department,
            system_attribute,
            go_live_date,
            decommission_date,
            major_milestones,
            industry_name,
            monitoring_system_name,
            system_function_summary,
            system_form,
            cs_client_name,
            bs_url,
            ip_port,
            business_line,
            system_category,
            system_level,
            has_backup_strategy,
            server_count,
            remarks,
            monitoring_coverage,
            digital_classification,
            jrt_0059_backup_standard,
            xinchuang_category_major,
            xinchuang_category_minor,
            software_copyright_name,
            construction_method,
            technical_route,
            operation_status,
            xinchuang_status,
            security_level,
            general_function_domains,
            futures_function_domains,
            is_reported_to_external,
            centos7_count, t.created_at, t.created_by, t.updated_at, t.updated_by
        FROM v_cmdb_system_admin_responsibility_company T
        WHERE ($1::text IS NULL or t.self_build_system_id LIKE $1) and
        ($4::text IS NULL or t.system_abbreviation LIKE $4) and
        ($5::text IS NULL or t.main_admin LIKE $5) and
        ($6::text IS NULL or t.system_attribute = $6) and
        ($7::text IS NULL or t.system_level = $7) and
        ($8::text IS NULL or t.jrt_0059_backup_standard LIKE $8) and
        ($9::text IS NULL or t.xinchuang_category_major LIKE $9) and
        ($10::text IS NULL or t.xinchuang_category_minor = $10) and
        ($11::text IS NULL or t.construction_method = $11) and
        ($12::text IS NULL or t.technical_route = $12) and
        ($13::text IS NULL or t.is_reported_to_external = $13) and
        ($14::text IS NULL or t.operation_status = $14) and
        ($15::text IS NULL or t.xinchuang_status = $15) and
        ($16::text IS NULL or t.security_level = $16) and
        ($17::text IS NULL or t.general_function_domains LIKE $17) and
        ($18::text IS NULL or t.futures_function_domains LIKE $18) and
        ($19::text IS NULL or t.business_line LIKE $19) and
        ($20::text IS NULL or t.system_category LIKE $20)
        ORDER BY ${sortProp} ${sortOrder.toUpperCase()},id desc LIMIT $2 OFFSET $3`;

        const queryParams = [self_build_system_id ? `%${self_build_system_id}%` : null, perPage, (page - 1) * perPage,
            system_abbreviation ? `%${system_abbreviation}%` : null,
            main_admin ? `%${main_admin}%` : null,
            system_attribute ? `${system_attribute}` : null,
            system_level ? `${system_level}` : null,
            jrt_0059_backup_standard ? `%${jrt_0059_backup_standard}%` : null,
            xinchuang_category_major ? `%${xinchuang_category_major}%` : null,
            xinchuang_category_minor ? `${xinchuang_category_minor}` : null,
            construction_method ? `${construction_method}` : null,
            technical_route ? `${technical_route}` : null,
            is_reported_to_external ? `${is_reported_to_external}` : null,
            operation_status ? `${operation_status}` : null,
            xinchuang_status ? `${xinchuang_status}` : null,
            security_level ? `${security_level}` : null,
            general_function_domain ? `%${general_function_domain}%` : null,
            futures_function_domain ? `%${futures_function_domain}%` : null,
            business_line ? `%${business_line}%` : null,
            system_category ? `%${system_category}%` : null
        ];

        const result = await connPG.query(sqlStr, queryParams);

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM v_cmdb_system_admin_responsibility_company T
            WHERE ($1::text IS NULL or t.self_build_system_id LIKE $1) and
            (1=1 or $2::text is null or $3::text is null) and
            ($4::text IS NULL or t.system_abbreviation LIKE $4) and
            ($5::text IS NULL or t.main_admin LIKE $5) and
            ($6::text IS NULL or t.system_attribute = $6) and
            ($7::text IS NULL or t.system_level = $7) and
            ($8::text IS NULL or t.jrt_0059_backup_standard LIKE $8) and
            ($9::text IS NULL or t.xinchuang_category_major = $9) and
            ($10::text IS NULL or t.xinchuang_category_minor = $10) and
            ($11::text IS NULL or t.construction_method = $11) and
            ($12::text IS NULL or t.technical_route = $12) and
            ($13::text IS NULL or t.is_reported_to_external = $13) and
            ($14::text IS NULL or t.operation_status = $14) and
            ($15::text IS NULL or t.xinchuang_status = $15) and
            ($16::text IS NULL or t.security_level = $16) and
            ($17::text IS NULL or t.general_function_domains LIKE $17) and
            ($18::text IS NULL or t.futures_function_domains LIKE $18) and
            ($19::text IS NULL or t.business_line LIKE $19) and
            ($20::text IS NULL or t.system_category LIKE $20)`,
            queryParams
        );

        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) });
    } catch (err) {
        console.error('查询系统管理员责任表（公司）失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});


app.post('/api/add_cmdb_system_admin_responsibility', (req, res) => {
    const fields = ['external_system_id',
        'system_abbreviation',
        'main_admin',
        'backup_admin',
        'production_attribute',
        'system_provider',
        'system_function_summary',
        'business_department',
        'system_form',
        'cs_client_name',
        'bs_url',
        'ip_port',
        'monitoring_system_name',
        'major_milestones',
        'created_by'
    ];

    // 使用扩展运算符从 req.body 中提取对应字段的值
    const { usernameby, ...params } = req.body;
    // 动态生成占位符
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
    // 构建 SQL 语句
    const sqlStr = `
       INSERT INTO cmdb_system_admin_responsibility (${fields.join(', ')})
       VALUES (${placeholders})
       RETURNING *;
   `;
    // 参数数组，确保包含 usernameby 对应的 created_by 字段
    const queryParams = fields.map(field => field === 'created_by' ? usernameby : params[field]);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('插入数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 返回插入的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/del_cmdb_system_admin_responsibility', (req, res) => {
    const id = req.body.id;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_system_admin_responsibility
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/update_cmdb_system_admin_responsibility', (req, res) => {
    const fields = [
        'external_system_id',
        'system_abbreviation',
        'main_admin',
        'backup_admin',
        'production_attribute',
        'system_provider',
        'system_function_summary',
        'business_department',
        'system_form',
        'cs_client_name',
        'bs_url',
        'ip_port',
        'monitoring_system_name',
        'major_milestones',
        'updated_at',  // 新增字段
        'updated_by',  // 新增字段
        'id'
    ];
    // 使用解构赋值从 req.body 中提取 id 和 usernameby
    const { id, usernameby, ...updates } = req.body;

    // 动态生成带有占位符的SQL语句
    const setClauseParts = [];
    const queryParams = [];

    fields.slice(0, -1).forEach((field, index) => {
        if (field === 'updated_at') {
            setClauseParts.push(`${field} = CURRENT_TIMESTAMP`);
        } else if (field === 'updated_by') {
            setClauseParts.push(`${field} = $${queryParams.length + 1}`);
            queryParams.push(usernameby);
        } else {
            if (updates[field] !== undefined) {
                setClauseParts.push(`${field} = $${queryParams.length + 1}`);
                queryParams.push(updates[field]);
            }
        }
    });

    const sqlStr = `
        UPDATE cmdb_system_admin_responsibility
        SET ${setClauseParts.join(',\n')}
        WHERE id = $${queryParams.length + 1}
        RETURNING *;
    `;

    // 将 id 添加到参数数组末尾
    queryParams.push(id);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('更新数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

// 获取功能域数据
app.post('/api/get_cmdb_function_domains', async (req, res) => {
    try {
        const function_domain_type = req.body.function_domain_type;

        let sqlStr = `
            SELECT
                id,
                primary_category_code,
                primary_category_name,
                function_domain_code,
                function_domain_name,
                is_core_domain,
                function_domain_type
            FROM
                cmdb_function_domain_types
            WHERE
                del_flag = '0' AND
                ($1::text IS NULL OR function_domain_type = $1)
            ORDER BY
                function_domain_code ASC
        `;

        const queryParams = [function_domain_type];
        const result = await connPG.query(sqlStr, queryParams);

        res.json({ code: 0, msg: result.rows });
    } catch (err) {
        console.error('查询功能域数据失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});

app.post('/api/get_cmdb_system_admin_responsibility', async (req, res) => {

    const system_abbreviation = req.body.system_abbreviation;
    const main_admin = req.body.main_admin;
    const production_attribute = req.body.production_attribute;

    const page = parseInt(req.body.currentPage);
    const perPage = parseInt(req.body.pageSize);
    const sortProp = req.body.sortProp;
    const sortOrder = req.body.sortOrder;

    let sqlStr = `SELECT t.id,
    external_system_id,
system_abbreviation,
main_admin,
backup_admin,
production_attribute,
system_provider,
system_function_summary,
business_department,
system_form,
cs_client_name,
bs_url,
ip_port,
monitoring_system_name,
major_milestones, t.created_at, t.created_by, t.updated_at, t.updated_by
FROM v_cmdb_system_admin_responsibility t
WHERE ($1::text IS NULL or t.system_abbreviation LIKE $1) and
($4::text IS NULL or t.main_admin LIKE $4) and
($5::text IS NULL or t.production_attribute = $5)
       ORDER BY ${sortProp} ${sortOrder.toUpperCase()},id desc LIMIT $2 OFFSET $3`;
    const queryParams = [system_abbreviation ? `%${system_abbreviation}%` : null, perPage, (page - 1) * perPage,
        main_admin ? `%${main_admin}%` : null,
        production_attribute ? `${production_attribute}` : null
    ];

    try {
        const result = await connPG.query(sqlStr, queryParams)

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM v_cmdb_system_admin_responsibility t
WHERE ($1::text IS NULL or t.system_abbreviation LIKE $1) and
(1=1 or $2::text is null or $3::text is null) and
($4::text IS NULL or t.main_admin LIKE $4) and
($5::text IS NULL or t.production_attribute = $5)`,
queryParams
        );
        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) })
    } catch (err) {
        console.error('查询管理IP失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});


app.post('/api/add_cmdb_data_dictionary', async (req, res) => {
    try {
        const fields = ['dict_type',
            'dict_type_name',
            'dict_code',
            'dict_name',
            'created_by'
        ];

        // 使用扩展运算符从 req.body 中提取对应字段的值
        const { usernameby, id, ...params } = req.body;

        // 首先检查是否存在相同的字典类型和字典代码组合
        const checkSql = `
            SELECT COUNT(*) as count
            FROM cmdb_data_dictionary
            WHERE dict_type = $1 AND dict_code = $2 AND del_flag = '0'
        `;

        const checkResult = await connPG.query(checkSql, [params.dict_type, params.dict_code]);

        // 如果存在相同的组合，返回错误
        if (checkResult.rows[0].count > 0) {
            return res.status(400).json({
                code: 1,
                msg: `字典类型 "${params.dict_type}" 下已存在字典代码 "${params.dict_code}"，请使用其他字典代码`
            });
        }

        // 检查并重置序列（如果需要）
        const maxIdQuery = `SELECT MAX(id) FROM cmdb_data_dictionary;`;
        const maxIdResult = await connPG.query(maxIdQuery);
        const maxId = maxIdResult.rows[0].max || 0;

        const seqValueQuery = `SELECT last_value FROM cmdb_data_dictionary_id_seq;`;
        const seqValueResult = await connPG.query(seqValueQuery);
        const currentSeqValue = seqValueResult.rows[0].last_value || 0;

        // 如果最大ID大于等于序列值，则重置序列
        if (maxId >= currentSeqValue) {
            const resetQuery = `SELECT setval('cmdb_data_dictionary_id_seq', ${maxId + 1}, false);`;
            await connPG.query(resetQuery);
            console.log(`序列已自动重置为: ${maxId + 1}`);
        }

        // 动态生成占位符
        const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');

        // 构建 SQL 语句 - 使用序列生成ID
        const sqlStr = `
           INSERT INTO cmdb_data_dictionary (${fields.join(', ')})
           VALUES (${placeholders})
           RETURNING *;
       `;

        // 参数数组，确保包含 usernameby 对应的 created_by 字段
        const queryParams = fields.map(field => field === 'created_by' ? usernameby : params[field]);

        // 执行查询
        const result = await connPG.query(sqlStr, queryParams);

        // 清除数据字典缓存，确保下次查询能获取最新数据
        clearDictionaryCache();

        // 返回插入的结果
        res.json({ code: 0, msg: result.rows[0] });
    } catch (error) {
        console.error('插入数据失败:', error);
        return res.status(500).json({ code: 1, msg: error.message });
    }
});

app.post('/api/del_cmdb_data_dictionary', (req, res) => {
    const id = req.body.id;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_data_dictionary
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 清除数据字典缓存，确保下次查询能获取最新数据
        clearDictionaryCache();

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/update_cmdb_data_dictionary', (req, res) => {
    const fields = ['dict_type',
        'dict_type_name',
        'dict_code',
        'dict_name',
        'updated_at',  // 新增字段
        'updated_by',  // 新增字段
        'id'
    ];
    // 使用解构赋值从 req.body 中提取 id 和 usernameby
    const { id, usernameby, ...updates } = req.body;

    // 首先检查是否存在相同的字典类型和字典代码组合（排除当前记录）
    const checkSql = `
        SELECT COUNT(*) as count
        FROM cmdb_data_dictionary
        WHERE dict_type = $1 AND dict_code = $2 AND id != $3 AND del_flag = '0'
    `;

    connPG.query(checkSql, [updates.dict_type, updates.dict_code, id], (checkErr, checkResult) => {
        if (checkErr) {
            console.error('检查数据失败:', checkErr);
            return res.status(500).json({ code: 1, msg: checkErr.message });
        }

        // 如果存在相同的组合，返回错误
        if (checkResult.rows[0].count > 0) {
            return res.status(400).json({
                code: 1,
                msg: `字典类型 "${updates.dict_type}" 下已存在字典代码 "${updates.dict_code}"，请使用其他字典代码`
            });
        }

        // 动态生成带有占位符的SQL语句
        const setClauseParts = [];
        const queryParams = [];

        fields.slice(0, -1).forEach((field) => {
            if (field === 'updated_at') {
                setClauseParts.push(`${field} = CURRENT_TIMESTAMP`);
            } else if (field === 'updated_by') {
                setClauseParts.push(`${field} = $${queryParams.length + 1}`);
                queryParams.push(usernameby);
            } else {
                if (updates[field] !== undefined) {
                    setClauseParts.push(`${field} = $${queryParams.length + 1}`);
                    queryParams.push(updates[field]);
                }
            }
        });

        const sqlStr = `
            UPDATE cmdb_data_dictionary
            SET ${setClauseParts.join(',\n')}
            WHERE id = $${queryParams.length + 1}
            RETURNING *;
        `;
        // 将 id 添加到参数数组末尾
        queryParams.push(id);

        // 执行查询
        connPG.query(sqlStr, queryParams, (err, result) => {
            if (err) {
                console.error('更新数据失败:', err);
                return res.status(500).json({ code: 1, msg: err.message });
            }

            // 检查是否更新成功
            if (result.rowCount === 0) {
                return res.json({ code: 1, msg: '未找到指定ID的数据' });
            }

            // 清除数据字典缓存，确保下次查询能获取最新数据
            clearDictionaryCache();

            // 返回更新的结果
            res.json({ code: 0, msg: result.rows[0] });
        });
    });
});

// 数据字典缓存
// 缓存结构: { dict_code: { data: [...], timestamp: Date.now() } }
const dictionaryCache = {};

// 重置数据字典表的序列值
app.post('/api/reset_cmdb_data_dictionary_sequence', async (req, res) => {
    try {
        // 1. 获取当前表中的最大ID值
        const maxIdQuery = `SELECT MAX(id) FROM cmdb_data_dictionary;`;
        const maxIdResult = await connPG.query(maxIdQuery);
        const maxId = maxIdResult.rows[0].max || 0;

        // 2. 获取序列的当前值
        const seqValueQuery = `SELECT last_value FROM cmdb_data_dictionary_id_seq;`;
        const seqValueResult = await connPG.query(seqValueQuery);
        const currentSeqValue = seqValueResult.rows[0].last_value || 0;

        console.log(`当前最大ID: ${maxId}, 当前序列值: ${currentSeqValue}`);

        // 3. 如果最大ID大于序列值，则重置序列
        if (maxId >= currentSeqValue) {
            // 将序列值设置为最大ID + 1
            const resetQuery = `SELECT setval('cmdb_data_dictionary_id_seq', ${maxId + 1}, false);`;
            await connPG.query(resetQuery);
            console.log(`序列已重置为: ${maxId + 1}`);

            return res.json({
                code: 0,
                msg: `序列已成功重置。原序列值: ${currentSeqValue}, 新序列值: ${maxId + 1}`
            });
        } else {
            return res.json({
                code: 0,
                msg: `序列无需重置。当前序列值(${currentSeqValue})已大于最大ID(${maxId})`
            });
        }
    } catch (error) {
        console.error('重置序列失败:', error);
        return res.status(500).json({ code: 1, msg: error.message });
    }
});
// 缓存过期时间，设置为30分钟
// 在生产环境中可以调整为更长时间，如果数据字典不经常变化
// 如果数据字典几乎不变，可以调整为更长时间，如1小时或更长
// 如果数据字典变化频繁，可以调整为更短时间，如几十秒
// 如果数据字典变化非常频繁，可以关闭缓存，或者在数据字典变化时主动清除缓存
// 如果数据字典非常大，可以考虑使用Redis等分布式缓存系统
// 如果数据字典非常小，可以考虑将所有数据字典加载到内存中
// 如果数据字典非常重要，可以考虑使用更复杂的缓存策略，如LRU策略或者多级缓存
// 如果数据字典非常不重要，可以考虑不使用缓存
const CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟

// 清除数据字典缓存的辅助函数
// 当数据字典发生变化时调用此函数
function clearDictionaryCache() {
    console.log('清除数据字典缓存');
    // 清空整个缓存对象
    for (const key in dictionaryCache) {
        delete dictionaryCache[key];
    }
}

// 添加测试路由来诊断问题
app.all('/api/test_method', (req, res) => {
    console.log('=== 测试路由调用 ===');
    console.log('请求方法:', req.method);
    console.log('请求路径:', req.path);
    console.log('请求体:', req.body);
    console.log('所有请求头:', req.headers);
    console.log('Nginx代理信息:');
    console.log('  X-Real-IP:', req.headers['x-real-ip']);
    console.log('  X-Forwarded-For:', req.headers['x-forwarded-for']);
    console.log('  X-Primary-Server:', req.headers['x-primary-server']);
    console.log('  X-Backup-Server:', req.headers['x-backup-server']);

    res.json({
        code: 0,
        msg: '测试成功',
        method: req.method,
        path: req.path,
        body: req.body,
        headers: req.headers,
        nginx_info: {
            real_ip: req.headers['x-real-ip'],
            forwarded_for: req.headers['x-forwarded-for'],
            primary_server: req.headers['x-primary-server'],
            backup_server: req.headers['x-backup-server']
        }
    });
});

// 添加通用路由处理器来捕获所有HTTP方法的数据字典请求
app.all('/api/get_cmdb_data_dictionary', async (req, res) => {
    // 添加调试日志
    console.log('=== 数据字典API调用 ===');
    console.log('请求方法:', req.method);
    console.log('请求路径:', req.path);
    console.log('请求URL:', req.url);
    console.log('请求体:', req.body);
    console.log('查询参数:', req.query);
    console.log('请求头User-Agent:', req.headers['user-agent']);
    console.log('请求头Content-Type:', req.headers['content-type']);
    console.log('请求头X-Forwarded-For:', req.headers['x-forwarded-for']);
    console.log('请求头X-Real-IP:', req.headers['x-real-ip']);
    console.log('请求头X-Primary-Server:', req.headers['x-primary-server']);
    console.log('请求头X-Backup-Server:', req.headers['x-backup-server']);
    console.log('原始请求方法（可能被代理修改）:', req.headers['x-original-method']);

    // 临时解决方案：如果是GET请求但有查询参数，尝试转换为POST处理
    if (req.method === 'GET' && req.query.dict_code) {
        console.log('检测到GET请求带有dict_code参数，尝试转换为POST处理');
        req.body = req.body || {};
        req.body.dict_code = req.query.dict_code;
        req.method = 'POST'; // 临时修改方法
        console.log('已将GET请求转换为POST处理，新的请求体:', req.body);
    }

    // 如果不是POST请求，返回405错误并提供详细信息
    if (req.method !== 'POST') {
        console.log('错误：不支持的HTTP方法:', req.method);
        const errorResponse = {
            code: 1,
            msg: `不支持的HTTP方法: ${req.method}，请使用POST方法`,
            receivedMethod: req.method,
            expectedMethod: 'POST',
            path: req.path,
            url: req.url,
            query: req.query,
            headers: {
                'user-agent': req.headers['user-agent'],
                'content-type': req.headers['content-type'],
                'x-forwarded-for': req.headers['x-forwarded-for'],
                'x-real-ip': req.headers['x-real-ip'],
                'x-primary-server': req.headers['x-primary-server'],
                'x-backup-server': req.headers['x-backup-server']
            },
            timestamp: new Date().toISOString(),
            suggestion: '请检查nginx配置或前端请求方法'
        };

        // 设置CORS头
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        return res.status(405).json(errorResponse);
    }

    const dict_code = req.body.dict_code;
    const dict_type = req.body.dict_type; // 添加对dict_type参数的支持
    const keyword = req.body.keyword; // 关键词搜索参数
    const page = parseInt(req.body.currentPage) || 1;
    const perPage = parseInt(req.body.pageSize) || 200;
    const sortProp = req.body.sortProp || 'dict_code';
    const sortOrder = req.body.sortOrder || 'desc';

    try {
        // 如果有关键词搜索，不使用缓存
        if (!keyword) {
            // 检查缓存是否有效
            // 使用dict_type或dict_code作为缓存键
            const cacheKey = dict_type || dict_code || 'all';
            const cachedData = dictionaryCache[cacheKey];
            const now = Date.now();

            if (cachedData && (now - cachedData.timestamp < CACHE_EXPIRY)) {
                console.log(`使用缓存的数据字典数据: ${cacheKey}`);
                // 使用缓存数据
                const total = cachedData.data.length;
                // 如果需要分页，在内存中处理
                let result;
                if (sortProp) {
                    // 在内存中排序
                    const sortedData = [...cachedData.data].sort((a, b) => {
                        if (sortOrder.toLowerCase() === 'asc') {
                            return a[sortProp] > b[sortProp] ? 1 : -1;
                        } else {
                            return a[sortProp] < b[sortProp] ? 1 : -1;
                        }
                    });
                    // 分页
                    const startIndex = (page - 1) * perPage;
                    result = sortedData.slice(startIndex, startIndex + perPage);
                } else {
                    // 不需要排序，直接分页
                    const startIndex = (page - 1) * perPage;
                    result = cachedData.data.slice(startIndex, startIndex + perPage);
                }

                return res.json({ code: 0, msg: result, total });
            }
        }

        // 缓存不存在或已过期，从数据库获取
        const cacheKey = keyword ? 'keyword_search' : (dict_type || dict_code || 'all');
        const now = Date.now(); // 定义当前时间戳
        console.log(`从数据库获取数据字典数据: ${cacheKey}`);

        // 根据不同的查询参数构建SQL
        let sqlStr, queryParams;

        // 优先使用dict_type参数进行精确查询
        if (dict_type) {
            // 精确查询字典类型
            sqlStr = `
                SELECT T.ID, T.dict_type, T.dict_type_name, T.dict_code, T.dict_name,
                to_char(T.created_at, 'yyyy-mm-dd hh24:mi:ss') created_at,
                t.created_by, to_char(T.updated_at, 'yyyy-mm-dd hh24:mi:ss') updated_at, t.updated_by
                FROM cmdb_data_dictionary T
                WHERE del_flag = '0' AND dict_type = $1
                ORDER BY ${sortProp} ${sortOrder.toUpperCase()}, id DESC
            `;
            queryParams = [dict_type];
        } else if (dict_code && dict_code.length <= 2 && !keyword) { // 假设字典类型代码很短，如A, B, C等
            // 精确查询字典类型
            sqlStr = `
                SELECT T.ID, T.dict_type, T.dict_type_name, T.dict_code, T.dict_name,
                to_char(T.created_at, 'yyyy-mm-dd hh24:mi:ss') created_at,
                t.created_by, to_char(T.updated_at, 'yyyy-mm-dd hh24:mi:ss') updated_at, t.updated_by
                FROM cmdb_data_dictionary T
                WHERE del_flag = '0' AND dict_type = $1
                ORDER BY ${sortProp} ${sortOrder.toUpperCase()}, id DESC
            `;
            queryParams = [dict_code];
        } else {
            // 模糊查询，支持关键词搜索
            sqlStr = `
                SELECT T.ID, T.dict_type, T.dict_type_name, T.dict_code, T.dict_name,
                to_char(T.created_at, 'yyyy-mm-dd hh24:mi:ss') created_at,
                t.created_by, to_char(T.updated_at, 'yyyy-mm-dd hh24:mi:ss') updated_at, t.updated_by
                FROM cmdb_data_dictionary T
                WHERE del_flag = '0'
                AND ($1::text IS NULL OR dict_code LIKE $1)
                AND ($2::text IS NULL OR LOWER(dict_type_name) LIKE LOWER($2) OR LOWER(dict_name) LIKE LOWER($2))
                ORDER BY ${sortProp} ${sortOrder.toUpperCase()}, id DESC
            `;
            queryParams = [
                dict_code ? `%${dict_code}%` : null,
                keyword ? `%${keyword}%` : null
            ];
        }

        // 获取所有数据，用于缓存
        const allDataResult = await connPG.query(sqlStr, queryParams);

        // 更新缓存
        dictionaryCache[cacheKey] = {
            data: allDataResult.rows,
            timestamp: now
        };

        // 分页处理
        const total = allDataResult.rows.length;
        const startIndex = (page - 1) * perPage;
        const paginatedResult = allDataResult.rows.slice(startIndex, startIndex + perPage);

        res.json({ code: 0, msg: paginatedResult, total });
    } catch (err) {
        console.error('查询数据字典失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});



app.post('/api/add_cmdb_users', async (req, res) => {
    const fields = ['username',
        'password',
        'role_code',
        'created_by'
    ];

    // 使用扩展运算符从 req.body 中提取对应字段的值
    const { username, password, role_code, usernameby } = req.body;
    try {

        // 加密密码
        const hashedPassword = await bcrypt.hash(password, 10); // 10 是 salt 的强度
        // 动态生成占位符
        const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
        // 构建 SQL 语句
        const sqlStr = `
       INSERT INTO cmdb_users (${fields.join(', ')})
       VALUES (${placeholders})
       RETURNING *;
   `;
        // 参数数组，将加密后的密码替换原始密码
        const queryParams = [username, hashedPassword, role_code, usernameby];

        // 执行查询
        const result = await connPG.query(sqlStr, queryParams);

        // 返回插入的结果
        res.json({ code: 0, msg: result.rows[0] });
    } catch (err) {

        console.error('插入数据失败:', err);

        return res.status(500).json({ code: 1, msg: err.message });
    }
});

app.post('/api/del_cmdb_users', (req, res) => {
    const id = req.body.id;
    const username = req.body.username;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_users
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1 and username <> 'admin'
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0 && username === 'admin') {
            return res.json({ code: 1, msg: '管理员账号不能删除' });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});


app.post('/api/update_cmdb_users', async (req, res) => {
    const { password, role_code, id, usernameby } = req.body;
    let sqlStr, queryParams;

    if (password) {
        // 加密密码
        const hashedPassword = await bcrypt.hash(password, 10); // 10 是 salt 的强度
        // 构建 SQL 语句及参数数组
        sqlStr = `
            UPDATE cmdb_users
            SET password = $1, role_code = $2,
            updated_at = CURRENT_TIMESTAMP, updated_by = '${usernameby}'
            WHERE id = $3
            RETURNING *;
        `;
        queryParams = [hashedPassword, role_code, id];
    } else {
        // 构建 SQL 语句及参数数组
        sqlStr = `
            UPDATE cmdb_users
            SET role_code = $1,
            updated_at = CURRENT_TIMESTAMP, updated_by = '${usernameby}'
            WHERE id = $2
            RETURNING *;
        `;
        queryParams = [role_code, id];
    }

    try {
        // 执行查询
        const result = await connPG.query(sqlStr, queryParams);

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    } catch (err) {
        console.error('更新数据失败:', err);
        return res.status(500).json({ code: 1, msg: err.message });
    }
});

app.post('/api/update_cmdb_users_password', async (req, res) => {
    const { password, username, currentPassword, usernameby } = req.body;

    try {
        // 1. 查询数据库以获取用户的现有密码
        const userQuery = `
            SELECT password
            FROM cmdb_users
            WHERE username = $1;
        `;
        const userResult = await connPG.query(userQuery, [username]);

        if (userResult.rows.length === 0) {
            return res.json({ code: 1, msg: '未找到指定用户的数据' });
        }

        const storedHashedPassword = userResult.rows[0].password;

        // 2. 验证提供的当前密码是否与数据库中的哈希密码匹配
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, storedHashedPassword);

        if (!isCurrentPasswordValid) {
            return res.json({ code: 1, msg: '当前密码不正确' });
        }

        // 3. 加密新密码
        const hashedPassword = await bcrypt.hash(password, 10);

        // 4. 更新用户的密码
        const updateQuery = `
            UPDATE cmdb_users
            SET password = $1,
            updated_at = CURRENT_TIMESTAMP, updated_by = $2
            WHERE username = $3
            RETURNING id, username, role_code, updated_at, updated_by;
        `;
        const queryParams = [hashedPassword, usernameby, username];

        const result = await connPG.query(updateQuery, queryParams);

        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定用户的数据' });
        }

        res.json({ code: 0, msg: '密码更新成功' });

    } catch (err) {
        console.error('更新数据失败:', err);
        return res.status(500).json({ code: 1, msg: err.message });
    }
});

app.post('/api/get_cmdb_users', async (req, res) => {

    const username = req.body.username;
    const page = parseInt(req.body.currentPage) || 1;
    const perPage = parseInt(req.body.pageSize) || 200;
    const loginUsername = req.body.loginUsername;
    const sortProp = req.body.sortProp;
    const sortOrder = req.body.sortOrder;

    let sqlStr = `SELECT T.ID,
	T.username,
	T.password,
	t.role_code,
	REPLACE(REPLACE(REPLACE(T.role_code,'I','增'),'D','删'),'U','改') role_code_name,
	to_char( T.created_at, 'yyyy-mm-dd hh24:mi:ss' ) created_at,
    t.created_by, to_char( T.updated_at, 'yyyy-mm-dd hh24:mi:ss' ) updated_at, t.updated_by
FROM
	cmdb_users T WHERE del_flag = '0' AND ($1::text IS NULL or username LIKE $1) and (username = $4 or $4 = 'admin')
       ORDER BY ${sortProp} ${sortOrder.toUpperCase()},id desc LIMIT $2 OFFSET $3`;
    const queryParams = [username ? `%${username}%` : null, perPage, (page - 1) * perPage, loginUsername];

    try {
        const result = await connPG.query(sqlStr, queryParams)

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM cmdb_users WHERE del_flag = '0' AND ($1::text IS NULL or username LIKE $1)`,
            [username ? `%${username}%` : null]
        );
        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) })
    } catch (err) {
        console.error('查询用户名失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});

// 引入LDAP认证服务
const ldapAuth = require('./services/ldapAuth');

// 修改登录接口
app.post('/api/get_cmdb_users_login', async (req, res) => {
    const username = req.body.username;
    const password = req.body.password;

    // 获取客户端IP地址
    const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    // 获取用户代理信息
    const userAgent = req.headers['user-agent'] || '';

    // 查询本地用户信息 - 只获取必要的字段，提高查询效率
    let sqlStr = `SELECT id, username, password, role_code FROM cmdb_users WHERE del_flag = '0' AND username = $1`;
    const queryParams = [username];

    try {
        // 设置查询超时
        const queryOptions = {
            statement_timeout: 5000 // 5秒超时
        };

        const result = await connPG.query(sqlStr, queryParams);

        // 如果本地用户不存在，直接返回错误
        if (result.rows.length === 0) {
            // 异步记录登录失败，不等待结果
            connPG.query(
                'INSERT INTO cmdb_user_login_logs(username, login_ip, login_status, login_device, remarks) VALUES($1, $2, $3, $4, $5)',
                [username, clientIp, 'failed', userAgent, '未找到登录账号']
            ).catch(logErr => {
                console.error('记录登录日志失败:', logErr);
            });

            return res.json({ code: 1, msg: '未找到登录账号' });
        }

        // 尝试LDAP认证
        let isAuthenticated = false;
        let authMethod = 'local';

        // 创建一个Promise.race来处理LDAP认证超时
        try {
            const ldapAuthPromise = ldapAuth.authenticate(username, password);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('LDAP认证超时')), 3000); // 3秒超时
            });

            // 使用Promise.race来处理超时
            isAuthenticated = await Promise.race([ldapAuthPromise, timeoutPromise]);

            if (isAuthenticated) {
                authMethod = 'ldap';
                console.log(`用户 ${username} 通过LDAP认证成功`);
            }
        } catch (ldapError) {
            console.error('LDAP认证过程中出错:', ldapError.message);
            // LDAP认证失败或超时，继续尝试本地认证
        }

        // 如果LDAP认证失败，尝试本地密码验证
        if (!isAuthenticated) {
            try {
                isAuthenticated = await bcrypt.compare(password, result.rows[0].password);
                if (isAuthenticated) {
                    console.log(`用户 ${username} 通过本地认证成功`);
                } else {
                    // 异步记录登录失败，不等待结果
                    connPG.query(
                        'INSERT INTO cmdb_user_login_logs(username, login_ip, login_status, login_device, remarks) VALUES($1, $2, $3, $4, $5)',
                        [username, clientIp, 'failed', userAgent, '密码不正确']
                    ).catch(logErr => {
                        console.error('记录登录日志失败:', logErr);
                    });

                    return res.json({ code: 1, msg: '密码不正确' });
                }
            } catch (bcryptError) {
                console.error('密码验证过程中出错:', bcryptError);
                return res.json({ code: 1, msg: '密码验证失败，请稍后再试' });
            }
        }

        // 认证成功，创建Token
        const token = jwt.sign(
            {
                userId: result.rows[0].id,
                username: result.rows[0].username,
                authMethod // 记录认证方式
            },
            JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN || '6h' }
        );

        // 异步记录登录成功，不等待结果
        connPG.query(
            'INSERT INTO cmdb_user_login_logs(username, login_ip, login_status, login_method, login_device) VALUES($1, $2, $3, $4, $5)',
            [username, clientIp, 'success', authMethod, userAgent]
        ).catch(logErr => {
            console.error('记录登录日志失败:', logErr);
        });

        // 查询用户的页面权限 - 异步获取，不阻塞登录响应
        const permissionQuery = `
            SELECT p.page_code, p.page_path
            FROM cmdb_pages p
            JOIN cmdb_user_page_permissions upp ON p.id = upp.page_id
            WHERE upp.user_id = $1
            AND p.del_flag = '0'
            AND upp.del_flag = '0'
        `;

        // 先返回登录成功响应，不等待权限查询
        res.json({
            code: 0,
            token,
            authMethod,
            user: {
                username: result.rows[0].username,
                role_code: result.rows[0].role_code
            }
        });

        // 异步查询权限，不影响登录响应速度
        try {
            const permissionResult = await connPG.query(permissionQuery, [result.rows[0].id]);
            console.log(`用户 ${username} 权限查询成功，共 ${permissionResult.rows.length} 条权限记录`);
        } catch (permErr) {
            console.error('查询用户权限失败:', permErr);
        }
    } catch (err) {
        console.error('查询用户登录信息失败:', err);

        // 异步记录登录异常，不等待结果
        connPG.query(
            'INSERT INTO cmdb_user_login_logs(username, login_ip, login_status, login_device, remarks) VALUES($1, $2, $3, $4, $5)',
            [username, clientIp, 'failed', userAgent, `系统错误: ${err.message}`]
        ).catch(logErr => {
            console.error('记录登录日志失败:', logErr);
        });

        return res.status(500).json({ code: 1, msg: err.message });
    }
});

app.post('/api/get_cmdb_dashboard', async (req, res) => {

    let sqlStr = `
SELECT asset_count,
		   asset_growth,
			 config_count,
			 config_growth
  from v_cmdb_dashboard`;

    try {
        const result = await connPG.query(sqlStr)

        res.json({ code: 0, msg: result.rows })
    } catch (err) {
        console.error('查询失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});

app.post('/api/get_cmdb_recent_activity', async (req, res) => {

    let sqlStr = `SELECT activity_info,
	activity_time,
	activity_type
FROM
	v_cmdb_user_logs T`;

    try {
        const result = await connPG.query(sqlStr)

        res.json({ code: 0, msg: result.rows })
    } catch (err) {
        console.error('查询失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});


app.post('/api/get_global_search', async (req, res) => {

    const query = req.body.query;
    const page = parseInt(req.body.currentPage) || 1;
    const perPage = parseInt(req.body.pageSize) || 200;
    const sortProp = req.body.sortProp || 'management_ip';
    const sortOrder = req.body.sortOrder || 'desc';

    // 使用视图查询
    const sqlStr = `
    SELECT *
    FROM v_cmdb_global_search
    WHERE management_ip LIKE $1
    ORDER BY ${sortProp} ${sortOrder.toUpperCase()}
    LIMIT $2 OFFSET $3
    `;

    const queryParams = [query ? `%${query}%` : '%', perPage, (page - 1) * perPage];

    try {
        const result = await connPG.query(sqlStr, queryParams);

        // 获取总记录数
        const countSql = `
        SELECT COUNT(DISTINCT management_ip) as total
        FROM v_cmdb_global_search
        WHERE management_ip LIKE $1
        `;

        const totalResult = await connPG.query(countSql, [query ? `%${query}%` : '%']);
        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) });

    } catch (err) {
        console.error('查询全局搜索失败:', err);
        res.status(500).send({ code: 1, msg: err.message });
    }
});


app.post('/api/get_cmdb_issue_collection', async (req, res) => {
    const { raised_time, raised_by, is_solved, solution_type, currentPage, pageSize, sortProp, sortOrder } = req.body;
    const page = parseInt(currentPage);
    const perPage = parseInt(pageSize);

    let sqlStr = `SELECT id, description,to_char(t.raised_time, 'yyyy-mm-dd'::text) raised_time, raised_by, is_solved, solution_type, solution,
                 solved_by,to_char(t.solved_time, 'yyyy-mm-dd'::text) solved_time, notification, to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) created_at, created_by,
                 to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) updated_at, updated_by, del_flag,
                 to_char(t.planned_dev_time, 'yyyy-mm-dd'::text) planned_dev_time, dev_cycle_days
                 FROM cmdb_issue_collection t
                 WHERE ($1::text IS NULL OR DATE_TRUNC('day', raised_time) = DATE_TRUNC('day', $1::timestamp))
                 AND ($2::text IS NULL OR raised_by LIKE $2)
                 AND ($3::text IS NULL OR is_solved = $3)
                 AND ($4::text IS NULL OR solution_type = $4)
                 AND del_flag = 0
                 ORDER BY ${sortProp || 'created_at'} ${sortOrder ? sortOrder.toUpperCase() : 'DESC'}, id DESC
                 LIMIT $5 OFFSET $6`;

    const queryParams = [
        raised_time ? raised_time : null,
        raised_by ? `%${raised_by}%` : null,
        is_solved ? is_solved : null,
        solution_type ? solution_type : null,
        perPage,
        (page - 1) * perPage
    ];

    try {
        const result = await connPG.query(sqlStr, queryParams);

        // 获取总记录数
        const totalResult = await connPG.query(
            `SELECT COUNT(*) as total FROM cmdb_issue_collection
             WHERE ($1::text IS NULL OR DATE_TRUNC('day', raised_time) = DATE_TRUNC('day', $1::timestamp))
             AND ($2::text IS NULL OR raised_by LIKE $2)
             AND ($3::text IS NULL OR is_solved = $3)
             AND ($4::text IS NULL OR solution_type = $4)
             AND del_flag = 0`,
            [raised_time ? raised_time : null, raised_by ? `%${raised_by}%` : null, is_solved ? is_solved : null, solution_type ? solution_type : null ]
        );

        res.json({ code: 0, msg: result.rows, total: parseInt(totalResult.rows[0].total) });
    } catch (err) {
        console.error('查询问题收集表失败:', err);
        res.status(500).json({ code: 1, msg: err.message });
    }
});


app.post('/api/add_cmdb_issue_collection', (req, res) => {
    const fields = ['description',
        'raised_time',
        'raised_by',
        'is_solved',
        'solution_type',
        'planned_dev_time',
        'dev_cycle_days',
        'solution',
        'solved_by',
        'solved_time',
        'notification',
        'created_by'
    ];

    // 使用扩展运算符从 req.body 中提取对应字段的值
    const { usernameby, ...params } = req.body;
    // 动态生成占位符
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
    // 构建 SQL 语句
    const sqlStr = `
       INSERT INTO cmdb_issue_collection (${fields.join(', ')})
       VALUES (${placeholders})
       RETURNING *;
   `;
    // 参数数组，确保包含 usernameby 对应的 created_by 字段，并处理日期字段的空字符串
    const queryParams = fields.map(field => {
        if (field === 'created_by') {
            return usernameby;
        } else if ((field === 'planned_dev_time' || field === 'raised_time' || field === 'solved_time') && params[field] === '') {
            return null; // 将日期字段的空字符串转换为null
        } else {
            return params[field];
        }
    });

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('插入数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 返回插入的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/del_cmdb_issue_collection', (req, res) => {
    const id = req.body.id;
    const usernameby = req.body.usernameby;

    // 构建带有占位符的SQL语句
    const sqlStr = `
        UPDATE cmdb_issue_collection
        SET del_flag = '1',updated_at = CURRENT_TIMESTAMP,updated_by = '${usernameby}'
        WHERE id = $1
        RETURNING *;  -- 返回被删除的数据
    `;

    // 参数数组
    const queryParams = [id];

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('删除数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否删除成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回删除的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

app.post('/api/update_cmdb_issue_collection', (req, res) => {
    const fields = ['description',
        'raised_time',
        'raised_by',
        'is_solved',
        'solution_type',
        'planned_dev_time',
        'dev_cycle_days',
        'solution',
        'solved_by',
        'solved_time',
        'notification',
        'updated_at',  // 新增字段
        'updated_by',  // 新增字段
        'id'
    ];
    // 使用解构赋值从 req.body 中提取 id 和 usernameby
    const { id, usernameby, ...updates } = req.body;

    // 动态生成带有占位符的SQL语句
    const setClauseParts = [];
    const queryParams = [];

    fields.slice(0, -1).forEach((field) => {
        if (field === 'updated_at') {
            setClauseParts.push(`${field} = CURRENT_TIMESTAMP`);
        } else if (field === 'updated_by') {
            setClauseParts.push(`${field} = $${queryParams.length + 1}`);
            queryParams.push(usernameby);
        } else {
            if (updates[field] !== undefined) {
                setClauseParts.push(`${field} = $${queryParams.length + 1}`);
                // 处理日期字段的空字符串
                if ((field === 'planned_dev_time' || field === 'raised_time' || field === 'solved_time') && updates[field] === '') {
                    queryParams.push(null); // 将日期字段的空字符串转换为null
                } else {
                    queryParams.push(updates[field]);
                }
            }
        }
    });

    const sqlStr = `
        UPDATE cmdb_issue_collection
        SET ${setClauseParts.join(',\n')}
        WHERE id = $${queryParams.length + 1}
        RETURNING *;
    `;

    // 将 id 添加到参数数组末尾
    queryParams.push(id);

    // 执行查询
    connPG.query(sqlStr, queryParams, (err, result) => {
        if (err) {
            console.error('更新数据失败:', err);

            return res.status(500).json({ code: 1, msg: err.message });
        }

        // 检查是否更新成功
        if (result.rowCount === 0) {
            return res.json({ code: 1, msg: '未找到指定ID的数据' });
        }

        // 返回更新的结果
        res.json({ code: 0, msg: result.rows[0] });
    });
});

// 用户登录记录查询接口
app.post('/api/get_cmdb_user_login_logs', async (req, res) => {
    const username = req.body.username;
    const login_status = req.body.login_status;
    const login_method = req.body.login_method;
    const start_date = req.body.start_date;
    const end_date = req.body.end_date;

    const page = parseInt(req.body.currentPage) || 1;
    const perPage = parseInt(req.body.pageSize) || 20;
    const sortProp = req.body.sortProp || 'login_time';
    const sortOrder = req.body.sortOrder || 'desc';

    let sqlStr = `
        SELECT
            id,
            username,
            to_char(login_time, 'yyyy-mm-dd hh24:mi:ss') as login_time,
            login_ip,
            login_status,
            login_method,
            login_device,
            remarks
        FROM
            cmdb_user_login_logs
        WHERE
            ($1::text IS NULL OR username LIKE $1) AND
            ($2::text IS NULL OR login_status = $2) AND
            ($3::text IS NULL OR login_method = $3) AND
            ($4::timestamp IS NULL OR login_time >= $4) AND
            ($5::timestamp IS NULL OR login_time <= $5)
        ORDER BY
            ${sortProp} ${sortOrder.toUpperCase()}
        LIMIT $6 OFFSET $7
    `;

    const queryParams = [
        username ? `%${username}%` : null,
        login_status || null,
        login_method || null,
        start_date || null,
        end_date ? `${end_date} 23:59:59` : null,
        perPage,
        (page - 1) * perPage
    ];

    try {
        const result = await connPG.query(sqlStr, queryParams);

        // 获取总记录数
        const totalSql = `
            SELECT COUNT(*) as total
            FROM cmdb_user_login_logs
            WHERE
                ($1::text IS NULL OR username LIKE $1) AND
                ($2::text IS NULL OR login_status = $2) AND
                ($3::text IS NULL OR login_method = $3) AND
                ($4::timestamp IS NULL OR login_time >= $4) AND
                ($5::timestamp IS NULL OR login_time <= $5)
        `;

        const totalResult = await connPG.query(totalSql, [
            username ? `%${username}%` : null,
            login_status || null,
            login_method || null,
            start_date || null,
            end_date ? `${end_date} 23:59:59` : null
        ]);

        res.json({
            code: 0,
            msg: result.rows,
            total: parseInt(totalResult.rows[0].total)
        });
    } catch (err) {
        console.error('查询用户登录记录失败:', err);
        res.status(500).json({ code: 1, msg: err.message });
    }
});







