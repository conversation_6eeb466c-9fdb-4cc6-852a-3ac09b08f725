-- 为所有用户添加交易日历页面权限和view权限
-- 执行时间：2025年5月29日

-- 1. 为所有用户添加交易日历页面权限
DO $$
DECLARE
    calendar_page_id INTEGER;
    user_record RECORD;
    permission_count INTEGER := 0;
BEGIN
    -- 获取交易日历页面ID
    SELECT id INTO calendar_page_id 
    FROM public.cmdb_pages 
    WHERE page_code = 'ops_calendar' 
    AND page_path = '/ops_calendar' 
    AND del_flag = '0';

    -- 检查是否找到交易日历页面
    IF calendar_page_id IS NULL THEN
        RAISE EXCEPTION '未找到交易日历页面，请先确保页面已创建';
    END IF;

    RAISE NOTICE '交易日历页面ID: %', calendar_page_id;

    -- 为所有用户添加交易日历页面权限
    FOR user_record IN 
        SELECT id, username, real_name 
        FROM public.cmdb_users 
        WHERE del_flag = '0'
    LOOP
        -- 检查用户是否已有该页面权限
        IF NOT EXISTS (
            SELECT 1 FROM public.cmdb_user_page_permissions 
            WHERE user_id = user_record.id 
            AND page_id = calendar_page_id 
            AND del_flag = '0'
        ) THEN
            -- 插入页面权限
            INSERT INTO public.cmdb_user_page_permissions 
                (user_id, page_id, created_at, created_by, updated_at, updated_by, del_flag)
            VALUES 
                (user_record.id, calendar_page_id, CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP, 'admin', '0');
            
            permission_count := permission_count + 1;
            RAISE NOTICE '为用户 % (%) 添加交易日历页面权限', user_record.username, user_record.real_name;
        ELSE
            RAISE NOTICE '用户 % (%) 已有交易日历页面权限', user_record.username, user_record.real_name;
        END IF;
    END LOOP;

    RAISE NOTICE '共为 % 个用户添加了交易日历页面权限', permission_count;
END $$;

-- 2. 为所有用户添加交易日历view权限
DO $$
DECLARE
    user_record RECORD;
    view_permission_count INTEGER := 0;
BEGIN
    RAISE NOTICE '开始为所有用户添加交易日历view权限...';

    -- 为所有用户添加交易日历view权限
    FOR user_record IN 
        SELECT id, username, real_name 
        FROM public.cmdb_users 
        WHERE del_flag = '0'
    LOOP
        -- 检查用户是否已有view权限
        IF NOT EXISTS (
            SELECT 1 FROM public.ops_calendar_duty_permissions 
            WHERE user_id = user_record.id 
            AND permission_type = 'view' 
            AND del_flag = '0'
        ) THEN
            -- 插入view权限
            INSERT INTO public.ops_calendar_duty_permissions 
                (user_id, permission_type, created_at, created_by, updated_at, updated_by, del_flag)
            VALUES 
                (user_record.id, 'view', CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP, 'admin', '0');
            
            view_permission_count := view_permission_count + 1;
            RAISE NOTICE '为用户 % (%) 添加交易日历view权限', user_record.username, user_record.real_name;
        ELSE
            RAISE NOTICE '用户 % (%) 已有交易日历view权限', user_record.username, user_record.real_name;
        END IF;
    END LOOP;

    RAISE NOTICE '共为 % 个用户添加了交易日历view权限', view_permission_count;
END $$;

-- 3. 验证权限添加结果
DO $$
DECLARE
    total_users INTEGER;
    page_permissions_count INTEGER;
    view_permissions_count INTEGER;
BEGIN
    -- 统计总用户数
    SELECT COUNT(*) INTO total_users 
    FROM public.cmdb_users 
    WHERE del_flag = '0';

    -- 统计有交易日历页面权限的用户数
    SELECT COUNT(DISTINCT upp.user_id) INTO page_permissions_count
    FROM public.cmdb_user_page_permissions upp
    JOIN public.cmdb_pages p ON upp.page_id = p.id
    WHERE p.page_code = 'ops_calendar' 
    AND p.del_flag = '0' 
    AND upp.del_flag = '0';

    -- 统计有交易日历view权限的用户数
    SELECT COUNT(DISTINCT user_id) INTO view_permissions_count
    FROM public.ops_calendar_duty_permissions 
    WHERE permission_type = 'view' 
    AND del_flag = '0';

    RAISE NOTICE '=== 权限添加结果统计 ===';
    RAISE NOTICE '系统总用户数: %', total_users;
    RAISE NOTICE '拥有交易日历页面权限的用户数: %', page_permissions_count;
    RAISE NOTICE '拥有交易日历view权限的用户数: %', view_permissions_count;
    
    IF page_permissions_count = total_users AND view_permissions_count = total_users THEN
        RAISE NOTICE '✓ 所有用户都已成功获得交易日历页面权限和view权限';
    ELSE
        RAISE NOTICE '⚠ 权限添加可能不完整，请检查';
    END IF;
END $$;

-- 4. 查询权限分配详情（可选，用于验证）
SELECT 
    u.username,
    u.real_name,
    CASE WHEN upp.id IS NOT NULL THEN '是' ELSE '否' END AS 有页面权限,
    CASE WHEN ocdp.id IS NOT NULL THEN '是' ELSE '否' END AS 有view权限
FROM public.cmdb_users u
LEFT JOIN public.cmdb_user_page_permissions upp ON u.id = upp.user_id 
    AND upp.page_id = (SELECT id FROM public.cmdb_pages WHERE page_code = 'ops_calendar' AND del_flag = '0')
    AND upp.del_flag = '0'
LEFT JOIN public.ops_calendar_duty_permissions ocdp ON u.id = ocdp.user_id 
    AND ocdp.permission_type = 'view' 
    AND ocdp.del_flag = '0'
WHERE u.del_flag = '0'
ORDER BY u.username;
